<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: beenzhang
 * Date: 2019/6/12
 * Time: 4:12 PM
 */

namespace common\library\lead;

use common\components\BaseObject;
use common\library\ai\classify\customer\advice\AiCustomerAdvice;
use common\library\customer_v3\customer\orm\Customer;
use common\library\custom_field\company_field\duplicate\CompanyMatchProcess;
use common\library\custom_field\company_field\duplicate\ConversionRules;
use common\library\custom_field\company_field\duplicate\MatchRules;
use common\library\custom_field\lead_field\duplicate\BuildLeadRules;
use common\library\custom_field\lead_field\duplicate\LeadMatchProcess;
use common\library\customer\FieldUniqueReadActions;
use common\library\customer\Helper;
use common\library\customer_convert\ConvertHandler;
use common\library\customer_convert\ConvertService;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\email\CommonDomain;
use common\library\email_identity\EmailIdentityList;
use common\library\lead_v3\LeadWriteService;
use common\library\setting\library\status\Status;
use common\library\trail\events\EdmEvents;
use common\library\trail\TrailConstants;
use common\library\util\TelUtil;
use common\library\customer_v3\company\orm\Company;
use Constants;

class LeadAutoArchive
{

    const LEAD_ASSISTANT_TEL_PREFIX = '电话猎手线索';

    protected $opUser;
    protected $clientId;
    protected $userId;

    protected $originId;
    protected $archiveType = 1;
    protected $contactLimit = 30;
    protected $filterDuplicateEmail = false;
    protected $email = [];
    protected $contactIds = [];
    protected $scene;

    public function __construct($userId)
    {
        $this->opUser = \User::getUserObject($userId);
        $this->clientId = $this->opUser->getClientId();
        $this->userId = $userId;
    }

    public function setOrigin($originId)
    {
        $this->originId = intval($originId);
    }

    public function setArchiveType($archiveType)
    {
        $this->archiveType = $archiveType;
    }

    public function setContactLimit($contactLimit)
    {
        $this->contactLimit = $contactLimit;
    }

    public function setEmail($email)
    {
        $this->email = $email;
    }

    public function setContactIds($ids) {
        $this->contactIds = $ids;
    }

    public function setScene($scene)
    {
        $this->scene = $scene;
    }

	/**
	 * @throws \ProcessException
	 * @throws \Exception
	 */
	public function archiveByHashId($companyHashId, $fromSdr = false)
    {
        $contacts = array_unique(array_merge($this->contactIds, $this->email));
        $result = \common\library\ai\Helper::getRecommendArchiveData($companyHashId, false, $this->contactLimit, $contacts, $this->scene, !$fromSdr);
        $leadData = $result['company']??[];
        $customerData = $result['customers']??[];
        if (empty($leadData)){
            \LogUtil::info("leadData is empty! userId:{$this->opUser->getUserId()} companyHashId=$companyHashId");
            return false;
        }
        // 拉取一次三方联系人数据
        if (empty($customerData)) {
            \common\library\ai\Helper::pullContactsData($this->clientId, $this->opUser->getUserId(), $companyHashId);
        }

        if ($fromSdr && empty($customerData)) {
            \LogUtil::info("companyHashId has no contact, skip fro sdr");
            return false;
        }

        //线索名称去除分号
        $leadData['name'] = str_replace(";", " ", $leadData['name']);

        $v = new \Validator();
        $v->setConfig(\CustomerOptionService::getValidatorRule());
        $v->setInputData($leadData);
        $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD));

        $lead = new Lead($this->clientId);
		//leads转线索的判重场景
		$lead->setDuplicateCheckScene(Constant::DUPLICATE_LEADS_SCENE);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->client_id = $this->clientId;
        $lead->name = $leadData['name'];
        $lead->company_name = $leadData['name'];
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->country = $leadData['country'] ?? '';
        $lead->scale_id = $leadData['scale_id'] ?? 0;
        $lead->tel = $leadData['tel'] ?? '';
        $lead->tel_area_code = $leadData['tel_area_code'] ?? '';
        $lead->province = $leadData['province'] ?? '';
        $lead->city = $leadData['city'] ?? '';
        $lead->address = $leadData['address'] ?? '';
        $lead->homepage = $leadData['homepage'] ?? '';
        $lead->remark = $leadData['remark'] ?? '';
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->company_hash_id = $companyHashId;
        $lead->is_archive = 1;
        $lead->archive_type = $this->archiveType;
		$lead->client_id = $this->clientId;
        if ($fromSdr) {
            $lead->is_archive = Lead::LEAD_TYPE_ARCHIVE;
            $lead->setFieldEditType(BaseObject::FIELD_EDIT_TYPE_BY_AI_SDR);
        }
        $lead->addUser($this->opUser->getUserId());

        $emails = array_unique(array_column($customerData,'email'));

        $leadProcess = new LeadMatchProcess($this->clientId);
        $companyProcess = new CompanyMatchProcess($this->clientId);
        $rules = $leadProcess->matchByCustomerField('email', $emails);
        if (!empty($rules)){
            $leadMatchedRules = $rules['matched_rules'];
            $customerRules = ConversionRules::leadCustomerRuleToCustomer($rules['customer_rules']);
            $companyMatchedRules = $companyProcess->matchByRules([],$customerRules);
            $companyRule = $companyMatchedRules[0]??[];
            $leadRule = $leadMatchedRules[0]??[];
            if (!(empty($leadRule) && empty($companyRule))){
                $showMatchedRule = $companyRule;
                if (!empty($showMatchedRule) && $showMatchedRule['unique_prevent']==1) {
                    $uniqueMessage = preg_split('/\s+/', trim($showMatchedRule['unique_message']), -1, PREG_SPLIT_NO_EMPTY);
                    $message = array_pop($uniqueMessage);
                    $uniqueMessage = array_values(array_unique($uniqueMessage));
                    $uniqueMessage = implode(', ', $uniqueMessage);
                    throw new \RuntimeException(\Yii::t('common', "{$uniqueMessage}{$message}"));
                }
            }
        }


        $customerList = [];
        foreach ($customerData as $contact) {
            $v->setInputData($contact);
            $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD_CUSTOMER));
            $customer = (new LeadCustomer($this->clientId));
            $customer->name = $contact['name'] ?? '';
            $customer->email = $contact['email'];
            $customer->post = $contact['post'] ?? '';
            $customer->tel_list = $contact['tel_list'] ?? [];
            if (empty($customerList) && $fromSdr) {
                $customer->main_customer_flag = 1;
            }
            $customerList[] = $customer;
        }

        if(empty($customerList)){//默认要有一个位置...
            $customer = new LeadCustomer($this->clientId);
            $customer->main_customer_flag = 1;
            $customer->email = '';
            $customerList = [$customer];
        }

        $lead->setCustomerList($customerList);
		//leads转线索的场景，同步中->(成功或者失败)
		$autoRecordId = LeadWriteService::saveAutoSystemCreateLog($lead, Constant::LEAD_TYPE_LEADS, $this->opUser->getUserId(), $lead->getMainCustomerEmail());
		$lead->setAutoRecordId($autoRecordId);
        $result = $lead->save();

        $opUserId = $this->opUser->getUserId();
        \LogUtil::info("{$this->clientId} opUser: {$opUserId} save status: {$result} lead_id: {$lead->lead_id} lead_user:".json_encode($lead->user_id));
		if (!$result) {
			return false;
		}
        return $lead;
    }

    public function archiveByBatchDomain($domains, $fromSdr = false) {
        $domainChunk = array_chunk($domains, 20);
        $domainLeads = [];
        foreach ($domainChunk as $domains) {
            $companyHashMap = $this->getHashIdByDomains($domains);
            foreach ($domains as $domain) {
                if (!isset($companyHashMap[$domain])) {
                    $domainLeads[$domain] = 0;
                    continue;
                }
                try {
                    $lead =  $this->archiveByHashId($companyHashMap[$domain], $fromSdr);
                    $domainLeads[$domain] = $lead;
                } catch (\Exception $exception) {
                    \LogUtil::info("client_id {$this->clientId} archive domain {$domain} to lead fail, exception {$exception->getMessage()}");
                }
            }
        }
        return $domainLeads;
    }

    public function archiveByEmailBatch($emailList){
        $emailList = \EmailUtil::findAllMailAddress(implode(";",$emailList));
        if (!$emailList)
            return false;

        $list = new CustomerList($this->clientId);
        $list->setEmail($emailList);
        $list->setIsArchive(true);
        $list->setFields(['email']);
        $customerList = $list->find();
        $customerEmailList = array_unique(array_column($customerList, 'email'));
        $emailList = array_values(array_diff($emailList, $customerEmailList));
        if (empty($emailList))
            return false;

        $customerList = [];
        foreach ($emailList as $email){
            $customer = new LeadCustomer($this->clientId);
            $customer->email = $email;
            $customer->main_customer_flag = empty($customerList) ? 1 : 0;
            $customerList[] = $customer;
        }

        $domain = \EmailUtil::getDomain($emailList[0]);
        if (!empty($domain)) {
            $domainVsHashId = $this->getHashIdByDomains([$domain]);
            $hashId = $domainVsHashId[$domain] ?? '';
        }

        $lead = new Lead($this->clientId);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->name = '合并线索：'.$emailList[0];
        $lead->company_name = '合并线索：'.$emailList[0];
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->is_archive = 1;
        $lead->company_hash_id = $hashId ?? '';
        $lead->archive_type = $this->archiveType;
        $lead->addUser($this->opUser->getUserId());
        $lead->setCustomerList($customerList);
        $lead->save();

        return $lead;
    }

    public function archiveByEmail($email, $taskId = 0, $behavior = '')
    {
        $email = \EmailUtil::findEmail($email);
        if (empty($email))
            return false;

        $list = new LeadCustomerList($this->clientId);
        $list->setFields('lead_id');
        $list->setUserId($this->opUser->getUserId());
        $list->setEmail($email);
        $customerCount = $list->count();

        $leadTypes = [
            \common\library\ai\classify\setting\ApplySettings::EDM_OPEN => Constant::LEAD_TYPE_EDM_OPEN,
            \common\library\ai\classify\setting\ApplySettings::EDM_REPLY => Constant::LEAD_TYPE_EDM_REPLY,
            \common\library\ai\classify\setting\ApplySettings::EDM_CLICK => Constant::LEAD_TYPE_EDM_CLICK_LINK,
            \common\library\marketing\site\SiteService::SITE_INQUIRY => Constant::LEAD_TYPE_WEBSITE,
        ];

        //邮箱已被建档到私海线索
        if ($customerCount > 0)
            return false;

        //邮箱已在公司范围内被建档为客户
        if (\common\library\customer\Helper::emailExists($this->clientId, $email)) return false;

        $domain = \EmailUtil::getDomain($email);

        //公共邮箱后缀--新建
        if (\common\library\email\CommonDomain::check($domain))
        {
            $customer = new LeadCustomer($this->clientId);
            $customer->email = $email;
            $customer->main_customer_flag = 1;

            $lead = new Lead($this->clientId);
            $lead->setOperatorUserId($this->opUser->getUserId());
            $lead->client_id = $this->clientId;
            $lead->name = $email;
            $lead->company_name = $email;
            $lead->origin_list = array_filter([$this->originId ?? 0]);
            $lead->create_user_id = $this->opUser->getUserId();
            $lead->is_archive = 1;
            $lead->archive_type = $this->archiveType;
            $lead->addUser($this->opUser->getUserId());
            $lead->setCustomerList([$customer]);

//            if ($lead->isNew() && !empty($behavior) && isset($leadTypes[$behavior])) {
//                $lead->setDuplicateCheckScene(Constant::DUPLICATE_AUTO_SCENE);
//                $lead->setAutoRecordId(LeadWriteService::saveAutoSystemCreateLog($lead, $leadTypes[$behavior], $this->opUser->getUserId(), $lead->getMainCustomerEmail()));
//            }

            if ($lead->save() && $taskId) {
                $this->saveLeadEdmTrail($this->clientId, $this->opUser->getUserId(), $lead->lead_id, $taskId, $email);
            }

            return $lead;
        }

        $list = new EmailIdentityList($this->opUser->getUserId());
        $list->setFields('email');
        $list->setDomains($domain);
        $list->setEmailType(EmailIdentityList::EMAIL_TYPE_OWNER_LEAD);
        $emailList = $list->find();

        //企业邮箱后缀已被建档于线索私海--合并
        if (!empty($emailList))
        {
            $emailList = array_column($emailList, 'email');
            $list = new LeadCustomerList($this->clientId);
            $list->setAlias('customer');
            $list->setFields('customer.lead_id');
            $list->setJoin( ' left join tbl_lead as lead on customer.lead_id = lead.lead_id ');
            $list->setUserId($this->opUser->getUserId());
            $list->setEmail($emailList);
            $list->setOrderBy(['order_time']);
            $list->setOrder('desc');
            $leadList = $list->find();

            $leadId = !empty($leadList) ? (reset($leadList))['lead_id'] : null;
            if (!$leadId)
                return false;

            $customer = new LeadCustomer($this->clientId);
            $customer->email = $email;

            $lead = new Lead($this->clientId, $leadId);
            $lead->setOperatorUserId($this->opUser->getUserId());
            $lead->addCustomer($customer);

            if ($lead->save() && $taskId) {
                $this->saveLeadEdmTrail($this->clientId, $this->opUser->getUserId(), $lead->lead_id, $taskId, $email);
            }

            return $lead;
        }

        //新建
        if (!empty($email) && !empty($domain)) {
            $domainVsHashId = $this->getHashIdByDomains([$domain]);
            $hashId = $domainVsHashId[$domain] ?? '';
        }

        $customer = new LeadCustomer($this->clientId);
        $customer->email = $email;
        $customer->main_customer_flag = 1;

        $lead = new Lead($this->clientId);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->client_id = $this->clientId;
        $lead->name = $email;
        $lead->company_name = $email;
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->is_archive = 1;
        $lead->company_hash_id = $hashId ?? '';
        $lead->archive_type = $this->archiveType;
        $lead->addUser($this->opUser->getUserId());
        $lead->setCustomerList([$customer]);

//        if ($lead->isNew() && !empty($behavior) && isset($leadTypes[$behavior])) {
//            $lead->setDuplicateCheckScene(Constant::DUPLICATE_AUTO_SCENE);
//            $lead->setAutoRecordId(LeadWriteService::saveAutoSystemCreateLog($lead, $leadTypes[$behavior], $this->opUser->getUserId(), $lead->getMainCustomerEmail()));
//        }

        if ($lead->save() && $taskId) {
            $this->saveLeadEdmTrail($this->clientId, $this->opUser->getUserId(), $lead->lead_id, $taskId, $email);
        }

        return $lead;
    }

    public function archiveByAdvice($adviceId)
    {
        if (empty($adviceId)) return false;
        $advice = (new AiCustomerAdvice($this->clientId))->loadById($adviceId);
        $adviceData = $advice->getData();
        $adviceData['customer'] = \ArrayUtil::index($adviceData['customer'] ?? [], 'email');
        if (!$advice->isExist() || !isset($adviceData['customer'][$advice->email])) return false;

        $companyData = $adviceData['company'];
        $customerData = $adviceData['customer'][$advice->email];

        $list = new LeadCustomerList($this->clientId);
        $list->setFields('lead_id');
        $list->setUserId($this->opUser->getUserId());
        $list->setEmail($advice->email);
        $customerCount = $list->count();

        // 邮箱已被建档到私海线索
        if ($customerCount > 0) {
            return false;
        }

        if (\common\library\customer\Helper::emailExists($this->clientId, $advice->email)) return false;

        // 合并企业邮箱后缀已被建档的私海线索
        $sameEnterpriseDomainLeadId = $this->getSameEnterpriseDomainLeadByEmail($advice->email);
        if ($sameEnterpriseDomainLeadId != false)
        {
            $leadCustomer = new LeadCustomer($this->clientId);
            $leadCustomer->email = $advice->email;

            $lead = new Lead($this->clientId, $sameEnterpriseDomainLeadId);
            $lead->setOperatorUserId($this->opUser->getUserId());
            $lead->addCustomer($leadCustomer);
            $lead->save();

            return $lead;
        }

        $leadCustomer = new LeadCustomer($this->clientId);
        $leadCustomer->email = $customerData['email'];
        $leadCustomer->main_customer_flag = 1;


        $lead = new Lead($this->clientId);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->name = $companyData['name'] ?? $advice->email;
        $lead->company_name = $companyData['name'] ?? $advice->email;
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->is_archive = 1;
        $lead->archive_type = $this->archiveType;
        $lead->addUser($this->opUser->getUserId());
        $lead->setCustomerList([$leadCustomer]);
        $lead->save();

        return $lead;
    }

    // 根据邮箱检测有无企业邮箱后缀已被建档的私海线索
    public function getSameEnterpriseDomainLeadByEmail($email)
    {
        $domain = \EmailUtil::getDomain($email);
        if (CommonDomain::check($domain)) return false;

        $emailIdentityList = new EmailIdentityList($this->opUser->getUserId());
        $emailIdentityList->setFields('email');
        $emailIdentityList->setDomains($domain);
        $emailIdentityList->setEmailType(EmailIdentityList::EMAIL_TYPE_OWNER_LEAD);
        $emailList = $emailIdentityList->find();
        if (!empty($emailList)) {
            $emailList = array_column($emailList, 'email');
            $list = new LeadCustomerList($this->clientId);
            $list->setAlias('customer');
            $list->setFields('customer.lead_id');
            $list->setJoin( ' left join tbl_lead as lead on customer.lead_id = lead.lead_id ');
            $list->setUserId($this->opUser->getUserId());
            $list->setEmail($emailList);
            $list->setOrderBy(['order_time']);
            $list->setOrder('desc');
            $leadList = $list->find();

            $leadId = !empty($leadList) ? (reset($leadList))['lead_id'] : null;
            return $leadId ? $leadId : false;
        }
        return false;
    }

    public function getHashIdByDomains(array $domains)
    {
        if (empty($domains))
            return [];

        try {
            $domains = array_unique($domains);
            $companyHashIds = \common\library\ai\service\RecommendService::getCompanyHashIdsByDomains($domains);
            $companyHashIdMap = array_column($companyHashIds, 'companyHashId', 'domain');
        } catch (\Exception $e) {
            $companyHashIdMap = [];
            \LogUtil::info('getCompanyHashIdsByDomains fail!' . $e->getMessage());
        }

        return $companyHashIdMap;
    }

    /**
     * EDM动态事件
     *
     * @param $clientId
     * @param $userId
     * @param $leadId
     * @param $taskId
     * @param $email
     */
    protected function saveLeadEdmTrail($clientId, $userId, $leadId, $taskId, $email)
    {
        $customerList = new \common\library\lead\LeadCustomerList($clientId);
        $customerList->setLeadId($leadId);
        $customerList->setEmail($email);
        $customerList->setFields(['is_archive', 'customer_id', 'company_id', 'lead_id', 'name', 'email']);
        $customerList->setIsArchive(\common\library\lead\Lead::ARCHIVE_OK);
        $leadMap = $customerList->find();
        $edmList = [];

        if (!empty($leadMap)) {
            $edmList = new \common\library\edm\EdmMailList($clientId, $taskId);
            $edmList->setStatus(\common\library\edm\EdmMail::STATUS_SUCCESS);
            $edmList->setFields('send_to, delivery_time');
            $list = $edmList->find();
            $edmList = array_column($list, 'delivery_time', 'send_to');
        }

        foreach ($leadMap as $item) {
            $leadId = $item['lead_id'];
            $customerIds = [$item['customer_id']];

            try {
                $trail = new EdmEvents();
                $trail->setType(TrailConstants::TYPE_EDM_SEND);
                $trail->setClientId($clientId);
                $trail->setCreateUser($userId);
                $trail->setUserId($userId);
                $trail->setLeadId($leadId);
                $trail->setLeadCustomerId($customerIds);
                $trail->setReferId($taskId);
                $trail->setCreateTime($edmList[$email]['delivery_time'] ?? date('Y-m-d H:i:s'));
                $trail->run();
            } catch (\RuntimeException $e) {
                \LogUtil::info("EDM动态生成失败，task_id={$taskId} " . $e->getMessage());
            }
        }

    }

    public function archiveByTelBatch(array $telList)
    {
        if (empty($telList))
            return false;

        $customerList = [];
        foreach ($telList as $tel){
            $telData = TelUtil::formatTelData($tel);
            $leadCustomer = (new LeadCustomer($this->clientId));
            $leadCustomer->main_customer_flag =  empty($customerList) ? 1 : 0;
            $leadCustomer->tel_list = [[$telData['tel_area_code'],$telData['tel']]];
            $leadCustomer->email = '';
            $customerList[] = $leadCustomer;
        }

        $lead = new Lead($this->clientId);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->name = self::LEAD_ASSISTANT_TEL_PREFIX;
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->is_archive = 1;
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->archive_type = $this->archiveType??\common\library\lead\Lead::ARCHIVE_TYPE_NORMAL;;
        $lead->addUser($this->opUser->getUserId());

        $lead->setCustomerList($customerList);
        $result = $lead->save();

        $opUserId = $this->opUser->getUserId();
        \LogUtil::info("[archiveByTelBatch] {$this->clientId} opUser: {$opUserId} save status: {$result} lead_id: {$lead->lead_id} lead_user:".json_encode($lead->user_id));
        if (!$result) {
            return false;
        }
        return $lead;
    }


    public function archiveByTel($tel)
    {
        if (empty($tel))
            return false;

        $list = new LeadCustomerList($this->clientId);
        $list->setFields('lead_id');
        $list->setUserId($this->opUser->getUserId());
        $list->setTel($tel);
        $customerCount = $list->count();

        //电话已被建档到私海线索
        if ($customerCount > 0)
            return false;

        $telData = TelUtil::formatTelData($tel);

        $lead = new Lead($this->clientId);
        $lead->setOperatorUserId($this->opUser->getUserId());
        $lead->name = self::LEAD_ASSISTANT_TEL_PREFIX;
        $lead->origin_list = array_filter([$this->originId ?? 0]);
        $lead->is_archive = 1;
        $lead->create_user_id = $this->opUser->getUserId();
        $lead->archive_type = $this->archiveType??\common\library\lead\Lead::ARCHIVE_TYPE_NORMAL;;
        $lead->addUser($this->opUser->getUserId());

        $leadCustomer = (new LeadCustomer($this->clientId));
        $leadCustomer->main_customer_flag = 1;
        $leadCustomer->tel_list = [[$telData['tel_area_code'],$telData['tel']]];
        $leadCustomer->email = '';

        $lead->setCustomerList([$leadCustomer]);
        $result = $lead->save();

        $opUserId = $this->opUser->getUserId();
        \LogUtil::info("[archiveByTel] {$this->clientId} opUser: {$opUserId} save status: {$result} lead_id: {$lead->lead_id} lead_user:".json_encode($lead->user_id));
        if (!$result) {
            return false;
        }
        return $lead;
    }

    public function batchCreateLeadByEmployeeIds($employeeIds, $is_merge)
    {
        try {
            \common\library\CommandRunner::run('lead', 'batchCreateLeadByEmployeeIds', [
                'clientId' => $this->clientId,
                'userId' => $this->userId,
                'employeeIds' => implode(",", $employeeIds),
                'isMerge' => $is_merge
            ]);
        } catch (\Exception $exception) {
            \LogUtil::info('archiveByEmployeeIds error:'.$exception->getMessage());
            \LogUtil::info('archiveByEmployeeIds error:'.$exception->getTraceAsString());
            throw new \RuntimeException("批量转化线索失败");
        }
        return count($employeeIds);
    }

    /**
     * 通过leads联系人 合并创建线索
     * @param array $employeeIds
     * @return Lead|false
     * @throws \ProcessException
     */
    public function archiveMergeByEmployeeIds(array $employeeIds)
    {
        if (empty($employeeIds))
            return false;
        $employeeApi = new \common\library\discovery\api\Employee($this->clientId, $this->userId);
        $employeeList = $employeeApi->getEmployeeDetailsByIds($employeeIds);
        if (empty($employeeList)){
            \LogUtil::info("employeeList is empty! userId={$this->userId} , employeeIds=".implode(',', $employeeIds));
            return false;
        }
        $company_hash_id = '';
        $allEmails = [];
        //取第一个公司id
        foreach ($employeeList as $employee){
            if(empty($company_hash_id)){
                $company_hash_id = $employee['company_hash_id'] ?? '';
            }
            $emails = $employee['emails'] ?? [];
            $allEmails = array_merge($allEmails, $emails);
        }
        $allEmails = array_values(array_unique($allEmails));
        if(!empty($company_hash_id)){
            $this->setContactLimit(100);
            $this->setEmail($allEmails);
            $this->setScene(\common\library\discovery\Constant::SCENE_CONTACTS);
            return $this->archiveByHashId($company_hash_id);
        }else{
            return $this->archiveByEmailBatch($allEmails);
        }
    }


	/**
	 * 通过leads联系人 合并创建线索
	 * @param array $employeeIds
	 * @return array
	 * @throws \ProcessException
	 */
	public function archiveGroupByEmployeeIds(array $employeeIds)
	{
		$failIds = [];
		$failCompanyHashIds = [];
		if (empty($employeeIds)) {
			return [[], [], []];
		}
		$employeeApi = new \common\library\discovery\api\Employee($this->clientId, $this->userId);
		$employeeList = $employeeApi->getEmployeeDetailsByIds($employeeIds);
		if (empty($employeeList)) {
			\LogUtil::info("employeeList is empty! userId={$this->userId} , employeeIds=" . implode(',', $employeeIds));
			return $employeeIds;
		}
		$emptyCompanyHashIdAllEmails = [];
		$emptyCompanyHashIdEmployeeIds = [];
		$emailsMap = [];
		$employeeIdsMap = [];
		//取第一个公司id
		foreach ($employeeList as $employee) {
			$emails = $employee['emails'] ?? [];
			if (empty($employee['company_hash_id'])) {
				$emptyCompanyHashIdAllEmails = array_merge($emptyCompanyHashIdAllEmails, $emails);
				$emptyCompanyHashIdEmployeeIds[] = $employee['employee_id'];
				continue;
			}
			$emailsMap[$employee['company_hash_id']] = array_merge($emailsMap[$employee['company_hash_id']] ?? [], $emails);
			$employeeIdsMap[$employee['company_hash_id']][] = $employee['employee_id'];
		}
		//已建档的线索或者客户都要过滤
		$companyHashIds = array_keys($emailsMap);
		$leadList = new \common\library\lead\LeadList($this->userId);
		$leadList->showAll(true);
		$leadList->setSkipPrivilege(true);
		$leadList->setFields(['company_hash_id']);
		$leadList->setCompanyHashId($companyHashIds);
		$existCompanyHashIds = array_column($leadList->find(), 'company_hash_id');

		$otherCompanyHashIds = array_diff($companyHashIds, $existCompanyHashIds);

		if (!empty($otherCompanyHashIds)) {
			$companyList = new CompanyList($this->userId);
			$companyList->showAll(true);
			$companyList->setSkipPrivilege(true);
			$companyList->setFields(['company_hash_id']);
			$companyList->setCompanyHashId($otherCompanyHashIds);
			$existCompanyHashIds = array_merge($existCompanyHashIds, array_column($companyList->find(), 'company_hash_id'));
		}

		$success = [];
		if (!empty($emailsMap)) {
			foreach ($emailsMap as $key => $emails) {
				if (in_array($key, $existCompanyHashIds)) {
					continue;
				}
				try {
					$this->setContactLimit(100);
					$this->setEmail($emails);
					$this->setScene(\common\library\discovery\Constant::SCENE_CONTACTS);
					$lead = $this->archiveByHashId($key);
					if (!$lead) {
						$failIds = array_merge($failIds, $employeeIdsMap[$key]);
						$failCompanyHashIds = array_merge($failCompanyHashIds, [$key]);
					} else {
						$success[$key] = $lead;
					}

				}catch (\Throwable $throwable) {
					\LogUtil::info('BatchCreateLeadByEmployeeIds error companyHashId:' . $key . $throwable->getMessage());
					$failIds = array_merge($failIds, $employeeIdsMap[$key]);
					$failCompanyHashIds = array_merge($failCompanyHashIds, [$key]);
				}
			}
		}
		if (!empty($emptyCompanyHashIdAllEmails)) {
			try {
				$lead = $this->archiveByEmailBatch($emptyCompanyHashIdAllEmails);

				if (!$lead) {
					$failIds = array_merge($failIds, $emptyCompanyHashIdEmployeeIds);
					$failCompanyHashIds = array_merge($failCompanyHashIds, ['']);
				} else {
					$success[] = $lead;
				}

			} catch (\Throwable $throwable) {
				\LogUtil::info('BatchCreateLeadByEmployeeIds error companyHashIdEmpty:' . $throwable->getMessage());
				$failIds = array_merge($failIds, $emptyCompanyHashIdEmployeeIds);
				$failCompanyHashIds = array_merge($failCompanyHashIds, ['']);
			}
		}

		return [$failIds, $success, $failCompanyHashIds];


	}

    /**
     *  通过leads联系人创建线索
     * @param $employeeId
     * @return Lead|false
     * @throws \ProcessException
     */
    public function archiveByEmployeeId($employeeId)
    {
        if (empty($employeeId))
            return false;
        $company = new \common\library\discovery\api\Employee($this->clientId, $this->userId);
        $employeeList = $company->getEmployeeDetailsByIds([$employeeId]);
        $employee = $employeeList[0] ?? [];
        if (empty($employee)){
            \LogUtil::info("employeeList is empty! userId={$this->userId} , employeeId={$employeeId}");
            return false;
        }
        $company_hash_id = $employee['company_hash_id'] ?? '';
        $emails = $employee['emails'] ?? [];
        if(!empty($company_hash_id)){
            $this->setContactLimit(100);
            $this->setEmail($emails);
            $this->setScene(\common\library\discovery\Constant::SCENE_CONTACTS);
            return $this->archiveByHashId($company_hash_id);
        }else{
            return $this->archiveByEmailBatch($emails);
        }
    }
    
	public function archiveByConvert($email, $taskId = 0, $behavior = '')
	{
		$email = \EmailUtil::findEmail($email);

		if (empty($email))
			return false;

		$list = new LeadCustomerList($this->clientId);
		$list->setFields('lead_id');
		$list->setUserId($this->opUser->getUserId());
		$list->setEmail($email);
		$customerCount = $list->count();

        $handler = new ConvertHandler($this->clientId, $this->opUser->getUserId(), \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING);
        $duplicateFlag = $handler->getDuplicateFlag();

		// 邮箱已被建档到私海线索 并且 未开启线索重复自动合并开关
		if ($customerCount > 0 && empty($duplicateFlag)) {
			return false;
		}

		$domain = \EmailUtil::getDomain($email);

		if (!empty($domain)) {
			$domainVsHashId = $this->getHashIdByDomains([$domain]);
			$hashId = $domainVsHashId[$domain] ?? '';
		}

		$data = [
			'company'  => [
				'company_name' => $email,
				'name'           => $email,
				'create_user_id' => $this->opUser->getUserId(),
				'company_hash_id' => $hashId ?? '',
				'origin_list' => array_filter([$this->originId ?? 0]),
				'archive_type' => \common\library\lead\Lead::ARCHIVE_TYPE_AI
			],
			'customer' => [
				[
					'email'              => $email,
					'main_customer_flag' => 1,
				],
			],
		];

        $callback = [
            \Constants::TYPE_COMPANY => [
                'setCheckQuotaFlag' => [false],
                'setCreateScene' => [Company::CREATE_SCENE_AUTO],
            ]];
		$convertObjectList = (new ConvertService($this->clientId, $this->opUser->getUserId()))->convert(\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING, [$this->opUser->getUserId()], $data, $callback);
		$lead = $convertObjectList[\Constants::TYPE_LEAD] ?? null;

		if ($lead && $taskId) {
			$this->saveLeadEdmTrail($this->clientId, $this->opUser->getUserId(), $lead->lead_id, $taskId, $email);
		}

		return $lead;
	}
}
