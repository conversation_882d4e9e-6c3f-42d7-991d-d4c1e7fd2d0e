<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * Author: <PERSON>(h<PERSON>)
 * Date: 2025/4/1
 * Time: 17:54
 */
namespace common\library\ai_sdr;

use Carbon\Carbon;
use common\components\BaseObject;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_agent\SdrEdmWriteAgent;
use common\library\ai_agent\SdrHutchLeadQualityAgent;
use common\library\ai_sdr\interfaces\RecommendApiInterface;
use common\library\ai_sdr\interfaces\AiAgentFactoryInterface;
use common\library\ai_sdr\profile\ClientProfile;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskStatisticHook;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\BatchAiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;
use common\library\ai_sdr\task_record\builder\AiSdrTaskRecordBuilder;
use common\library\AiBackgroundService;
use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer\form\CustomerInputForm;
use common\library\customer_convert\ConvertException;
use common\library\customer_convert\ConvertService;
use common\library\customer_v3\company\list\CompanyList;
use common\library\discovery\api\Company;
use common\library\discovery\CompanyService;
use common\library\edm\EdmMailList;
use common\library\edm\EdmTask;
use common\library\lead\Lead;
use common\library\lead\LeadCustomer;
use common\library\lead_v3\LeadList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\recommend_plaza\RecommendApi;
use common\library\setting\library\tag\Tag;
use common\tests\functional\app\NotificationReadTest;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Workflow\DefinitionBuilder;
use Symfony\Component\Workflow\Event\Event;
use Symfony\Component\Workflow\Event\GuardEvent;
use Symfony\Component\Workflow\StateMachine;
use Symfony\Component\Workflow\Transition;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Workflow\MarkingStore\SingleStateMarkingStore;
use Symfony\Component\Workflow\MarkingStore\MarkingStoreInterface;
use xiaoman\orm\database\data\NotEqual;

class SdrDetailExecutor {

    protected int $clientId;
    protected int $userId;
    protected StateMachine $workflow;
    protected EventDispatcher $eventDispatcher;
    protected $successList = [];
    protected $failedList = [];
    protected $data = [];

    protected $clientProfile = [];
    protected $edmMail = '';
    protected AiSdrTask $task;
    protected $currentEdmCount = 0;
    protected $current;
    public static int $needBackgroundRecheck = 1;

    protected $isAdvance = true;

    // 依赖注入的服务
    protected ?RecommendApiInterface $recommendApi = null;
    protected ?AiAgentFactoryInterface $aiAgentFactory = null;

    public function setClientProfile(array $clientProfile) {
        $this->clientProfile = $clientProfile;
    }

    public function setEdmMailAddress(string $edmMail) {
        $this->edmMail = $edmMail;
    }

    public function setTask(AiSdrTask $task) {
        $this->task = $task;
    }

    public function setCurrentEdmCount(int $edmCount) {
        $this->currentEdmCount = $edmCount;
    }

    public function __construct(
        int $clientId,
        int $userId,
        ?RecommendApiInterface $recommendApi = null,
        ?AiAgentFactoryInterface $aiAgentFactory = null
    ) {
        $this->clientId = $clientId;
        $this->userId = $userId;

        $this->workflow = $this->loadWorkflow();
        $this->isAdvance = \common\library\privilege_v3\Helper::hasFunctional($this->clientId, PrivilegeConstants::FUNCTIONAL_AI_SDR_ADVANCE);

        // 依赖注入，如果没有提供则使用默认实现
        $this->recommendApi = $recommendApi ?? new RecommendApi($this->clientId, $this->userId);
        $this->aiAgentFactory = $aiAgentFactory; // 仅在测试时注入
    }

    /**
     * 创建质量分析Agent
     *
     * @return mixed
     */
    protected function createQualityAnalysisAgent()
    {
        if ($this->aiAgentFactory !== null) {
            return $this->aiAgentFactory->createQualityAnalysisAgent();
        }
        return new \common\library\ai_agent\SdrLeadQualityAnalysisAgent($this->clientId, $this->userId);
    }

    /**
     * 创建Hutch线索质量Agent
     *
     * @return mixed
     */
    protected function createHutchLeadQualityAgent()
    {
        if ($this->aiAgentFactory !== null) {
            // 在测试环境中，可以返回相同的Mock Agent
            return $this->aiAgentFactory->createQualityAnalysisAgent();
        }
        return new SdrHutchLeadQualityAgent($this->clientId, $this->userId);
    }

    /**
     * 创建EDM写作Agent
     *
     * @return mixed
     */
    protected function createEdmWriteAgent()
    {
        if ($this->aiAgentFactory !== null) {
            return $this->aiAgentFactory->createEdmWriteAgent();
        }
        return new SdrEdmWriteAgent($this->clientId, $this->userId);
    }

    protected function addWorkflowListeners() {
        $this->eventDispatcher->addListener('workflow.guard', function (GuardEvent $event) {
            /**
             * @var $detail SdrLeadDetail
            */
            $detail = $event->getSubject();
            $transition = $event->getTransition();
            $valid = $this->validateTransition($detail, $transition);
            \LogUtil::info("task_id: {$this->task->task_id} detail_id: {$detail->id} validate transition: {$transition->getName()} valid: {$valid}");
            if (!$valid) {
                $event->setBlocked(true);
            }
        });

        $this->eventDispatcher->addListener('workflow.completed', function (Event $event) {
            $transition = $event->getTransition();
            $this->performAction($event->getSubject(), $transition);
        });
    }

    protected function getNextTransition($nextStatus) : ?string {
        $transitions = $this->workflow->getDefinition()->getTransitions();
        foreach ($transitions as $transition) {
            $tos = $transition->getTos();
            if (in_array($nextStatus, $tos)) {
                return $transition->getName();
            }
        }
        return null;
    }

    public function process(array $processDetailList, int $nextStatus) {
        $this->successList = [];
        $this->failedList = [];

        $nextTransition = $this->getNextTransition($nextStatus);
        if (!$nextTransition) {
            throw new \RuntimeException("cannot find next valid status");
        }
        foreach ($processDetailList as $processDetail) {
            if ($this->workflow->can($processDetail, $nextTransition)) {
                $this->workflow->apply($processDetail, $nextTransition);
            }
        }

        $batchDetail = new BatchAiSdrTaskDetail($this->clientId);
        if (!empty($this->failedList)) {
            $batchDetail->initFromData(SdrLeadDetail::getListAttributes($this->failedList, 'id'));
            $batchDetail->getOperator()->update(['status' => Constant::DETAIL_STATUS_ERROR]);
        }
        return $processDetailList;
    }

    protected function updateDetail(SdrLeadDetail &$detail, $nextStatus, $updateData = []) {
        $oldStage = $detail->stage;

        $detail->status = $nextStatus;

        // 如果更新数据中包含stage，也更新detail对象
        if (isset($updateData['stage'])) {
            $detail->stage = $updateData['stage'];
        }

        $batchDetail = new BatchAiSdrTaskDetail($this->clientId);
        $finalUpdateData = array_merge([
            'status' => $nextStatus,
            'update_time' => xm_function_now(),
        ], $updateData);

        if (!empty($updateData['enable_flag']) && empty($detail->enable_flag)) {
            $finalUpdateData['stage_dig_time'] = xm_function_now();
            AISdrService::updateStatTotal($this->clientId, $this->task->task_id, 1);
        }

        if (isset($updateData['stage']) && $oldStage !== $updateData['stage']) {
            $timeField = Constant::TASK_DETAIL_STAGE_TIME_MAP[$updateData['stage']] ?? '';
            if ($timeField) {
                if ($detail->{$timeField} <= '1970-01-01 00:00:01') {
                    $finalUpdateData[$timeField] = $detail->{$timeField} = xm_function_now();
                }
            }
            if ($updateData['stage'] == Constant::AI_SDR_STAGE_DIG) {
            }
        }

        $batchDetail->getOperator()->batchUpdate([$detail->id => $finalUpdateData]);
    }

    protected function performAction(object $detail, Transition $nextTransition) {
        /**
         * @var SdrLeadDetail $detail
        */
        \LogUtil::info("task_id: {$this->task->task_id} detail_id: {$detail->id} perform action: {$nextTransition->getName()}");
        $nextAction = $nextTransition->getName();
        switch ($nextAction) {
            case 'label_lead_quality':
                if (in_array($detail->source, [Constant::TASK_SOURCE_AI_SDR, Constant::TASK_SOURCE_CRM_EP])) {
                    $agent = $this->createQualityAnalysisAgent();
                } else {
                    $agent = $this->createHutchLeadQualityAgent();
                }
                $answer = $agent->process(['client_profile' => json_encode($this->clientProfile), 'buyer_profile' => json_encode($detail->buyer_profile)]);
                $leadQuality = $answer['answer']??[
                    'domain' => '',
                    'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
                    'reason' => [],
                    'confidence' => 0.0,
                    'reason_detail' => [],
                ];

                $builder = new AiSdrTaskRecordBuilder($this->clientId, $this->task->task_id);
                $builder->build($detail->id, $detail->lead_id, Constant::RECORD_TYPE_ANALYZE_QUALITY, $leadQuality['reason']);
                $this->updateDetail($detail, $nextTransition->getTos()[0], ['lead_quality' => $leadQuality['lead_quality']]);
                break;
            case 'start_background_check':
                $companyProfile = $detail->company_info;
                $logoUrl = $this->needDownloadLogo($companyProfile);
                if ($logoUrl) {
                    try {
                        $file = \UploadService::downloadUploadFromUrl($logoUrl);
                    } catch (\Exception $exception) {
                        \LogUtil::info("lead_id {$detail->lead_id} download logo error", [
                            'client_id' => $this->clientId,
                            'lead_id' => $detail->lead_id,
                            'exception' => $exception->getMessage(),
                        ]);
                    }
                    if (isset($file['file_id'])) {
                        $lead = new Lead($this->clientId, $detail->lead_id);
                        $lead->image_list = [$file['file_id']];
                        $lead->save();
                    }
                }

                if (!$this->task->isDig() || self::$needBackgroundRecheck) {
                    $params = [
                        'company_name' => $detail->company_info['company_name'] ?? '',
                        'company_homepage' => $detail->origin_domain,
                    ];
                    $aiBackgroundService = new AiBackgroundService();
                    try {
                        $aiBackgroundReferType = AiBackgroundService::REFER_TYPE_AI_SDR_FREE;
                        if (\common\library\privilege_v3\Helper::hasFunctional($this->clientId, PrivilegeConstants::FUNCTIONAL_AI_SDR_ADVANCE)) {
                            $aiBackgroundReferType = AiBackgroundService::REFER_TYPE_AI_SDR;
                        }
                        $result = $aiBackgroundService->createTask($this->clientId, $this->userId, $aiBackgroundReferType, $detail->id, $params);
                        $redis = \RedisService::aiCache();
                        $key = sprintf(Constant::TASK_DAILY_LIMIT_CACHE_KEY, $this->clientId, $this->task->task_id);

                        $now = Carbon::now();
                        $nextMidnight = $now->copy()->endOfDay()->addSeconds(1);
                        $secondsUntilMidnight = $now->diffInSeconds($nextMidnight);

                        $luaScript = <<<LUA
local current = redis.call('GET', KEYS[1])
if current then
    current = tonumber(current) + 1
else
    current = 1
end
redis.call('SETEX', KEYS[1], tonumber(ARGV[1]), current)
return current
LUA;

                        $redis->eval($luaScript, 1, $key,  $secondsUntilMidnight);
                    } catch (\Exception $exception) {
                        \LogUtil::info("lead_id {$detail->lead_id} create background check task error", [
                            'client_id' => $this->clientId,
                            'lead_id' => $detail->lead_id,
                            'exception' => $exception->getMessage(),
                        ]);
                    }
                    $backgroundTaskId = $result['task_id']??0;
                    if ($backgroundTaskId) {
                        $builder = new AiSdrTaskRecordBuilder($this->clientId, $this->task->task_id);
                        $builder->build($detail->id, $detail->lead_id, Constant::RECORD_TYPE_BACKGROUND_CHECK, [
                            'background_task_id' => $backgroundTaskId,
                            'params' => $params
                        ]);
                        // 需要执行 ai 背调，但是成功创建了 task 才流转到下一状态
                        $this->updateDetail($detail, $nextTransition->getTos()[0]);
                    }
                } else {
                    // 不需要执行重新 ai 背调，直接流转到下一状态
                    $this->updateDetail($detail, $nextTransition->getTos()[0]);
                }

                break;
            case 'validate_contact':
                $updateData = [];
                if ($this->task->isDig()
                    && in_array($detail->lead_quality, [Constant::LEAD_QUALITY_HIGH, Constant::LEAD_QUALITY_MEDIUM])) {
                    $updateData = [
                        'enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
                    ];
                }
                $updateData['stage'] = Constant::AI_SDR_STAGE_REACHABLE;

                // 每个潜客必定要走到，所以

                if (self::$needBackgroundRecheck) {
                    $lead = new Lead($this->clientId, $detail->lead_id);
                    if (!$lead->isNew()) {
                        AISdrService::addSdrTagToLead($lead, $this->task, $detail->lead_quality);
                    }
                }

                $report = [];
                if ($this->task->isDig()) {
                    $recordFilter = new AiSdrTaskRecordFilter($this->clientId);
                    $recordFilter->type = Constant::RECORD_TYPE_BACKGROUND_CHECK;
                    $recordFilter->detail_id = $detail->id;
                    $recordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
                    $recordFilter->order('create_time', 'desc');
                    $recordFilter->limit(1);
                    $recordFilter->select(['data']);
                    $data = $recordFilter->rawData()[0]['data']??[];
                    if (empty($data['report'])) {
                        \LogUtil::info("lead_id {$detail->lead_id} has no background check record, cannot validate contact");
                        return;
                    }
                    $report = $data['report'];
                }

                $contacts = [];
                $api = $this->recommendApi;
                $emails = array_column($detail->company_contact, 'email');
                if (isset($report['company_extra_info']['key_employees'])) {
                    $emails = array_merge($emails, array_column($report['company_extra_info']['key_employees'], 'email'));
                }
                $validateEmailResult = array_fill_keys($emails, \common\library\auto_market\Constant::MAIL_LEVEL_UNKNOWN);
                try {
                    $validateEmailResult = $api->getMailQualityRating($emails);
                } catch (\Throwable $exception) {
                    \LogUtil::info("ai sdr validate email error", [
                        'client_id' => $this->clientId,
                        'lead_id' => $detail->lead_id,
                        'exception' => $exception->getMessage(),
                    ]);
                }

                // 仅保留有效联系人
                $removeCustomerList = [];
                foreach ($detail->company_contact as $contact) {
                    $email = $contact['email'];
                    $isEmailValid = $validateEmailResult[$email]??\common\library\auto_market\Constant::MAIL_LEVEL_UNKNOWN;
                    if ($isEmailValid > \common\library\auto_market\Constant::MAIL_LEVEL_UNKNOWN) {
                        $removeCustomerList[] = $contact;
                    }
                    $contacts[] = [
                        'name' => $contact['name'],
                        'title' => $contact['post'],
                        'email' => $email,
                        'isKeyContact' => 0,
                        'isEmailValid' => $isEmailValid,
                        'source' => $this->task->source,
                    ];
                }
                $addCustomerList = [];
                $v = new \Validator();
                $v->setConfig(\CustomerOptionService::getValidatorRule());
                foreach ($report['company_extra_info']['key_employees']??[] as $contact) {
                    $isEmailValid = $validateEmailResult[$contact['email']]??\common\library\auto_market\Constant::MAIL_LEVEL_UNKNOWN;
                    $contacts[] = [
                        'name' => $contact['name']??'',
                        'title' => $contact['title']??"",
                        'email' => $contact['email']??'',
                        'isKeyContact' => 1,
                        'isEmailValid' => $isEmailValid,
                        'source' => $this->task->source,
                    ];

                    if ($isEmailValid <= \common\library\auto_market\Constant::MAIL_LEVEL_UNKNOWN) {
                        try {
                            $v->setInputData($contact);
                            $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD_CUSTOMER));
                            $leadCustomer = new LeadCustomer($this->clientId);
                            $leadCustomer->name = $contact['name']??'';
                            $leadCustomer->email = $contact['email']??'';
                            $leadCustomer->post = $contact['title']??'';
                            $addCustomerList[] = $leadCustomer;
                        } catch (\RuntimeException $e) {
                            \LogUtil::info("lead_id {$detail->lead_id} add customer error", [
                                'client_id' => $this->clientId,
                                'lead_id' => $detail->lead_id,
                                'customer' => json_encode($contact),
                                'exception' => $e->getMessage(),
                            ]);
                        }
                    }
                }

                if (!empty($removeCustomerList)) {
                    foreach ($removeCustomerList as $contact) {
                        $customer = new LeadCustomer($this->clientId, $contact['customer_id']);
                        $customer->setFieldEditType(BaseObject::FIELD_EDIT_TYPE_BY_AI_SDR);
                        if ($customer->isExist()) {
                            $customer->remove(true);
                        }
                    }
                }
                // 关键联系人补充
                if (!empty($addCustomerList)) {
                    $lead = new Lead($this->clientId, $detail->lead_id);
                    $lead->setFieldEditType(BaseObject::FIELD_EDIT_TYPE_BY_AI_SDR);
                    $lead->setCustomerList($addCustomerList);
                    $result = $lead->save();
                    \LogUtil::info("{$this->clientId}  save status: {$result} lead_id: {$lead->lead_id} lead_user:".json_encode($lead->user_id));
                }

                $builder = new AiSdrTaskRecordBuilder($this->clientId, $this->task->task_id);
                $builder->build($detail->id, $detail->lead_id, Constant::RECORD_TYPE_CHECK_CONTACTS, $contacts);
                $this->updateDetail($detail, $nextTransition->getTos()[0], $updateData);
                $this->checkTaskStage($detail, Constant::AI_SDR_STAGE_REACHABLE);
                break;
            case 'start_marketing':
                $companyProfile = $detail->buyer_profile;
                $tryCount = 3;
                $agent = $this->createEdmWriteAgent();
                for ($i = 0; $i < $tryCount; $i++) {
                    try {
                        $emailContents = $agent->process(['client_profile' => json_encode($this->clientProfile, JSON_UNESCAPED_UNICODE), 'buyer_profile' => json_encode($companyProfile, JSON_UNESCAPED_UNICODE)]);
                        break; // 成功
                    } catch (AiAgentException $exception) {
                        \LogUtil::info("lead_id {$detail->lead_id} generate email content error", [
                            'client_id' => $this->clientId,
                            'lead_id' => $detail->lead_id,
                            'exception' => $exception->getMessage(),
                        ]);
                    }
                }
                if (empty($emailContents)) {
                    \LogUtil::info("lead_id {$detail->lead_id} generate email content error after retry {$tryCount} times");
                    return;
                }

                $emailContents = $emailContents['answer'];

                // 非正式版 sdr 只处理第一轮营销
                if (!$this->isAdvance) {
                    $emailContents = array_slice($emailContents, 0, 1);
                }

                $tomorrowTimestamp = strtotime('+1 day');
                foreach ($emailContents as $index => &$emailContent) {
                    $emailContent['round'] = $index+1;
                    switch ($index) {
                        case 0:
                            $date = date('Y-m-d', $tomorrowTimestamp);
                            $emailContent['estimate_time'] = $date;
                            break;
                        case 1:
                            $estimateTime = $tomorrowTimestamp+7*24*3600;
                            $emailContent['estimate_time'] = date('Y-m-d', $estimateTime);
                            break;
                        case 2:
                            $emailContent['estimate_time'] = date('Y-m-d', $tomorrowTimestamp+14*24*3600);
                            break;
                        default:
                            $emailContents['estimate_time'] = date('Y-m-d', $tomorrowTimestamp);
                            break;
                    }
                }

                $builder = new AiSdrTaskRecordBuilder($this->clientId, $this->task->task_id);
                $builder->build($detail->id, $detail->lead_id, Constant::RECORD_TYPE_CREATE_MARKET_PLAN, $emailContents);

                $this->updateDetail($detail, $nextTransition->getTos()[0], ['stage' => Constant::AI_SDR_STAGE_MARKETING]);
                $this->checkTaskStage($detail, Constant::AI_SDR_STAGE_MARKETING);
                break;
            case 'execute_marketing':
                $recordFilter = new AiSdrTaskRecordFilter($this->clientId);
                $recordFilter->limit(1);
                $recordFilter->detail_id = $detail->id;
                $recordFilter->type = Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN;
                $recordFilter->getQuery()->rawWhere(' AND executed_time IS NULL');
                $recordFilter->select(['record_id', 'data', 'estimate_time']);
                $recordFilter->order('estimate_time', 'ASC');
                $records = $recordFilter->rawData();
                $estimateTime = $records[0]['estimate_time']??'';
                if (empty($records) || empty($estimateTime)) {
                    \LogUtil::info("lead_id {$detail->lead_id} has no execute marketing record or estimate time, cannot start marketing");
                    return;
                }
                if (strtotime($estimateTime) > time()) {
                    \LogUtil::info("lead_id {$detail->lead_id} execute marketing estimate time is not reached, cannot start marketing");
                    return;
                }

                $emailContent = $records[0]['data']??[];
                $contacts = array_column($detail->getSendContactList(), 'email');
                $companyProfile = $detail->company_info;

                $task = new EdmTask($this->clientId);
                $task->setOpUserId($this->userId);
                $type = empty($this->edmMail) ?'noMailId' :'new';
                $task->initTaskFrom(0, $type);
                $task->saveHiddenDraft();
                $task->setAddress($contacts);

                $task->setSubject('AI SDR营销任务'.($companyProfile['company_name']??'').xm_function_date());
                $task->setMailSubjects([$emailContent['subject']]);
                $task->setType(EdmTask::TYPE_SDR);
                $task->setStatus(EdmTask::STATUS_WAIT_START);

                $task->setSenderName('');
                $task->setSenderMail($this->edmMail);
                $task->setParamMailContent($emailContent['content']);
                $task->setEdmFilterFlag(EdmTask::FILTER_ALL);
                $task->setCustomerArchiveFlag(EdmTask::CUSTOMER_FILTER_TYPE_ALL);
                $task->setParamMailBusinessType(EdmTask::BUSINESS_TYPE_MARKET);

                $task->start_time = date('Y-m-d H:i:s');
                $task->draft_flag = EdmTask::DRAFT_FLAG_NO;

                // 忽略 beforeSave 附件处理
                $task->ignoreProcessAttachment();
                $task->setParamAiWriteFlag(0);
                // 同步发送，不另起脚本（可能突发起了太多进程，或有时候会挂）
                $task->commit(false);
                // 单独处理附件
                $task->processAttachment(0);
                $task->processAttachment(1);

                $task->processDecompose();
                $task->processSubmit();

                $this->currentEdmCount -= count($detail->getSendContactList());
                $record = new AiSdrTaskRecord($this->clientId, $records[0]['record_id']);
                $record->refer_id = $task->task_id;
                $record->refer_type = Constant::RECORD_REFER_TYPE_EDM;
                $record->executed_time = $record->update_time = xm_function_now();
                $record->update(['executed_time', 'update_time', 'refer_id', 'refer_type']);
                $this->updateDetail($detail, $nextTransition->getTos()[0]);
                break;
            case 'listen_marketing_result':
                // No record
                $this->updateDetail($detail, $nextTransition->getTos()[0], ['stage' => Constant::AI_SDR_STAGE_EFFECTIVE]);
                $this->checkTaskStage($detail, Constant::AI_SDR_STAGE_EFFECTIVE);
                break;
            case 'listen_high_value_leads':
                $this->updateDetail($detail, $nextTransition->getTos()[0], ['stage' => Constant::AI_SDR_STAGE_HIGHVALUE]);
                $this->checkTaskStage($detail, Constant::AI_SDR_STAGE_HIGHVALUE);
                break;
            case 'end':
                $record = new AiSdrTaskRecordBuilder($this->clientId, $this->task->task_id);
                $record->build($detail->id, $detail->lead_id, Constant::RECORD_TYPE_HATCH_ABORT, [
                    'end_stage' => $this->task->end_stage
                ]);
                $this->updateDetail($detail, $nextTransition->getTos()[0]);
                break;
            default:
                break;
        }
        \LogUtil::info("task_id: {$this->task->task_id} detail_id: {$detail->id} perform action success: {$nextTransition->getName()}");
    }

    /**
     * @param SdrLeadDetail $detail
     * @param Transition $transition
     * @return bool
     */
    protected function validateTransition($detail, Transition $transition) {
        $transitionName = $transition->getName();
        // 挖掘任务在挖掘阶段的话允许
        if ($this->task->end_stage != Constant::AI_SDR_STAGE_DIG && $detail->stage >= $this->task->end_stage ) {
            \LogUtil::info("lead_id {$detail->lead_id} has already finished task, cannot process anymore");
            return false;
        }
        /* @var SdrLeadDetail $detail */
        switch ($transitionName) {
            case "validate_contact":
                if (($this->task->isDig() && $detail->status == Constant::DETAIL_STATUS_BACKGROUND_CHECKING)
                || ($this->task->source == Constant::TASK_SOURCE_IMPORT && $detail->status == Constant::DETAIL_STATUS_LABEL)) {
                    return true;
                } else {
                    return false;
                }
            case 'start_marketing':
                // 如果潜客没有联系人
                if (empty($detail->getSendContactList())) {
                    return false;
                }
                // edm 额度不足，不限制营销计划生成
                return true;
            case 'execute_marketing':
                // edm 额度不足，block 但是不加入失败
                if ($this->currentEdmCount < count($detail->getSendContactList())) {
                    return false;
                }
                return true;
            case 'label_lead_quality':
                if ($this->task->isDig()) {
                    if ($detail->status != Constant::DETAIL_STATUS_ADD) {
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if ($detail->status == Constant::DETAIL_STATUS_ADD) {
                        \LogUtil::info("lead {$detail->lead_id} has not been check background, cannot label lead quality");
                        return false;
                    }
                    if (empty($detail->buyer_profile)) {
                        \LogUtil::info("lead_id {$detail->lead_id} has no domain and buyer profile, cannot label lead quality");
                        return false;
                    } else {
                        return true;
                    }
                }
            case 'start_background_check':
                if ($this->task->isDig()) {
                    // 开关控制是否对挖掘任务开启二次背调
                    if ($detail->status == Constant::DETAIL_STATUS_BACKGROUND_CHECKING && !self::$needBackgroundRecheck)  {
                        \LogUtil::info("lead_id {$detail->lead_id} not allow to recheck background for dig record");
                        return false;
                    }
                }
                if (empty($detail->company_info) && empty($detail->origin_domain)) {
                    $flag = false;
                } else {
                    if (empty($detail->company_info['homepage']) && empty($detail->origin_domain)) {
                        $flag = !empty($detail->company_info['company_name']);
                    } else {
                        $detail->origin_domain = !empty($detail->origin_domain)?$detail->origin_domain:$detail->company_info['homepage'];
                        $flag = true;
                    }
                }
                if (!$flag) {
                    \LogUtil::info("detail_id {$detail->id} lead_id {$detail->lead_id} has no homepage or domain, cannot start background check");
                    return false;
                }

                $redis = \RedisService::aiCache();
                $count = $redis->get(sprintf(Constant::TASK_DAILY_LIMIT_CACHE_KEY, $this->clientId, $this->task->task_id));
                $flag = !($count >= Constant::DAILY_LIMIT);
                if (!$flag) {
                    \LogUtil::info("task_id {$this->task->task_id} detail_id {$detail->id} reach daily limit");
                }

                return $flag;
            case 'end':
                if (!$this->current) {
                    $this->current = new \DateTime(xm_function_now());
                }
                $detailCreateTime = new \DateTime($detail->create_time);
                $diff = $this->current->diff($detailCreateTime);
                $day = intval($diff->format('%a'));
                if ($day >= 30) {
                    return true;
                }

                return false;
            default:
                break;
        }
        return true;
    }

    protected function loadWorkflow() {
        $path = 'application.library.ai_sdr.config.workflow';
        $configPath = \Yii::getPathOfAlias($path) . '.yaml';
        $config = Yaml::parse(file_get_contents($configPath));

        $builder = new DefinitionBuilder();
        $builder->addPlaces($config['places']);

        foreach ($config['transitions'] as $name => $transition) {
            $froms = is_array($transition['from']) ? $transition['from'] : [$transition['from']];
            foreach ($froms as $from) {
                $builder->addTransition(new Transition($name, $from, $transition['to']));
            }
        }

        $this->eventDispatcher = new EventDispatcher();
        $this->addWorkflowListeners();

        $markingStore = new SingleStateMarkingStore('status');
        return new StateMachine($builder->build(), $markingStore, $this->eventDispatcher);
    }

    /**
     * @params $old_end_stage 修改前的交付阶段
     */
    public function checkTaskStage(SdrLeadDetail $detail, $stage, $old_end_stage = 0): void
    {
        if ($this->task->task_status == Constant::AI_SDR_TASK_STATUS_DRAFT) {
            \LogUtil::info("lead_id {$detail->lead_id} has finished hutching, but task_id {$this->task->task_id} is a draft task");
            return;
        }

        $task_id = $this->task->task_id;
        $endStage = $this->task->end_stage;
        $source = $this->task->source;
        $delivery_as = $this->task->delivery_as;    //交付配置 0-线索、1-客户
        $is_automatic_conversion = $this->task->is_automatic_conversion;

        $lead_id = $detail->lead_id;

        $batchDetail = new BatchAiSdrTaskDetail($this->clientId);
        $batchDetail->initFromData([['id' => $detail->id]]);

        if ($stage >= $endStage) {  //已完成孵化
            $data = ['stage' => $endStage];
            if ($old_end_stage) {
                $data['old_end_stage'] = $old_end_stage;
            }

            (new AiSdrTaskRecordBuilder($this->clientId, $task_id))->build($detail->id, $lead_id, Constant::RECORD_TYPE_HATCH_SUCCESS, $data);

            //更新潜客状态
            $batchDetail->getOperator()->update(['delivery_status' => Constant::DETAIL_DELIVERY_STATUS_HATCH_SUCCESS]);

            //如果没有选择将线索自动转化为客户，或所选对象本已为客户；则节点只有完成孵化和放弃孵化，无已建档、疑似重复
            if ($source == Constant::TASK_SOURCE_IMPORT && !$is_automatic_conversion){
                return;
            }

            //交付配置
            $validReferType = null;
            if ($this->task->isDig()){
                if ($delivery_as == Constant::AI_SDR_TASK_DELIVERY_TYPE_LEAD){
                    $validReferType = \Constants::TYPE_LEAD;
                }

                if ($delivery_as == Constant::AI_SDR_TASK_DELIVERY_TYPE_COMPANY){
                    $validReferType = \Constants::TYPE_COMPANY;
                }
            }

            if ($source == Constant::TASK_SOURCE_IMPORT && $is_automatic_conversion) {
                $validReferType = \Constants::TYPE_COMPANY;
            }

            if (!$validReferType){
                \LogUtil::info('AI SDR Delivery Fail', ['task_id' => $task_id, 'lead_id' => $lead_id, 'task' => $this->task->getAttributes()]);
                return;
            }

            //判重
            $lead = new Lead($this->clientId, $lead_id);
            $lead->getFormatter()->setShowCustomer(true);
            $lead = $lead->getAttributes();

            try{
                (new FieldUniqueValidator($this->clientId, $validReferType, []))
                    ->setPreventOnly(true)
                    ->setAttributes($lead)
                    ->validate();
            } catch (\Exception $e){
                \LogUtil::info('AI SDR Valid Fail', ['task_id' => $task_id, 'lead_id' => $lead_id, 'errCode' => $e->getCode(), 'errMsg' => $e->getMessage(), 'validFailMessages' => $e->getValidFailMessages()]);

                $validFailMessages = $e->getValidFailMessages();
                $validFailMessages = array_filter($validFailMessages, function ($item) use ($validReferType) {
                    if ($item['field_refer_type'] == $validReferType && !empty($item['refer_id'])) {
                        return true;
                    }
                    return false;
                });

                $refer_id = $validFailMessages[0]['refer_id'] ?? 0;
                if(!$refer_id){
                    return;
                }

                $recordData = [
                    'refer_type' => $validReferType == \Constants::TYPE_COMPANY ? Constant::RECORD_REFER_TYPE_CONTACT : Constant::RECORD_REFER_TYPE_LEAD,
                    'refer_id' => $refer_id,
                ];

                (new AiSdrTaskRecordBuilder($this->clientId, $task_id))->build($detail->id, $lead_id, Constant::RECORD_TYPE_DELIVERY_FAILED, $recordData);

                $batchDetail->getOperator()->update(['delivery_status' => Constant::DETAIL_DELIVERY_STATUS_DELIVERY_FAILED]);
                return;
            }

            //处理异常情况
            $lead['pool_id'] = 0;
            $lead['remark'] = $lead['remark'] ?? '';

            $main_customer_email = $lead['main_customer_email'];
            $main_customer_flag = false;

            $customers = $lead['customers'] ?? [];
            foreach ($customers as &$customer){
                if ($customer['email'] == $main_customer_email){    //main_customer_flag都为0会导致创建客户失败
                    $customer['main_customer_flag'] = 1;
                    $main_customer_flag = true;
                }
                $customer['remark'] = $customer['remark'] ?? '';    //备注为null会导致创建客户失败
            }

            if ($customers && !$main_customer_flag){
                $customers[0]['main_customer_flag'] = 1;
            }

            if ($this->task->end_stage >= Constant::AI_SDR_STAGE_EFFECTIVE) {
                $recordFilter = new AiSdrTaskRecordFilter($this->clientId);
                $recordFilter->detail_id = $detail->id;
                $recordFilter->type = Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN;
                $recordFilter->refer_type = Constant::RECORD_REFER_TYPE_EDM;
                $recordFilter->refer_id = new NotEqual(0);
                $recordFilter->select(['refer_id']);
                $edmMailIds = array_column($recordFilter->rawData(), 'refer_id');
                if (!empty($edmMailIds)) {
                    $edmMailList = new EdmMailList($this->clientId, $edmMailIds);
                    if ($this->task->end_stage == Constant::AI_SDR_STAGE_EFFECTIVE) {
                        $edmMailList->setIsOpened(true, false);
                    } else {
                        $edmMailList->setIsReply(true);
                    }
                    $edmMailList->setFields('send_to');
                    $validCustomerMail = array_column($edmMailList->find(), 'send_to');
                }
            }

            $validCustomers = $customers;
            if (isset($validCustomerMail)) {
                // 仅交付对营销内容有返回的联系人 （邮件打开或回复）
                $validCustomers = array_filter($customers, function ($customer) use ($validCustomerMail) {
                    return in_array($customer['email'], $validCustomerMail);
                });
            }

            // 执行交付动作
            if ($source == Constant::DETAIL_SOURCE_DIG){
                $convertData = [
                    'company' => [
                        'lead_id' => $lead_id,
                        'name' => $lead['name'],
                        'company_name' => $lead['company_name'],
                        'origin_list' => $lead['origin_list'],
                        'country' => $lead['country'],
                        'scale_id' => $lead['scale_id'],
                        'tel' => $lead['tel'],
                        'tel_area_code' => $lead['tel_area_code'],
                        'province' => $lead['province'],
                        'city' => $lead['city'],
                        'address' => $lead['address'],
                        'homepage' => $lead['homepage'],
                        'remark' => $lead['remark'],
                        'company_hash_id' => $lead['company_hash_id'],
                        'task_id' => $task_id,
                        'recommend_main_product_ids' => $detail->product_ids,
                        'create_user_id' => $this->userId,
                    ],
                    'customer' => $validCustomers,
                ];

                try {
                    $convertService = new ConvertService($this->clientId, $this->userId);
                    $convertObjectList = $convertService->convert(\common\library\setting\library\origin\Origin::SYS_ORIGIN_AI_SDR, [$this->userId], $convertData);
                } catch (\Throwable $throwable) {
                    \LogUtil::info('AI SDR Convert Fail', ['task_id' => $task_id, 'lead_id' => $lead_id, 'errCode' => $throwable->getCode(), 'errMsg' => $throwable->getMessage(), 'convertData' => $convertData, 'throwable' => $throwable->getTraceAsString()]);
                    return;
                }

                \LogUtil::info('checkTaskStage', ['task_id' => $task_id, 'lead_id' => $lead_id, 'convertObjectList' => $convertObjectList]);

                if ($delivery_as == Constant::AI_SDR_TASK_DELIVERY_TYPE_LEAD) {
                    $lead = $convertObjectList[\Constants::TYPE_LEAD] ?? null;
                    if (empty($lead)) {
                        \LogUtil::info('AI SDR Convert Fail, Lead Is Empty', ['task_id' => $task_id, 'lead_id' => $lead_id]);
                        return;
                    }

                    $recordData = [
                        'refer_id' => $lead_id,
                        'refer_type' => Constant::RECORD_REFER_TYPE_LEAD,
                        'stage' => $endStage,
                    ];

                    (new AiSdrTaskRecordBuilder($this->clientId, $task_id))->build($detail->id, $lead_id, Constant::RECORD_TYPE_DELIVERY_SUCCESS, $recordData);

                    $batchDetail->getOperator()->update(['delivery_status' => Constant::DETAIL_DELIVERY_STATUS_DELIVERY_SUCCESS]);
                    return;
                }

                if ($delivery_as == Constant::AI_SDR_TASK_DELIVERY_TYPE_COMPANY) {
                    $company = $convertObjectList[\Constants::TYPE_COMPANY] ?? null;
                    if (empty($company)) {
                        \LogUtil::info('AI SDR Convert Fail, Company Is Empty', ['task_id' => $task_id, 'lead_id' => $lead_id]);
                        return;
                    }

                    $recordData = [
                        'refer_id' => $company->company_id,
                        'refer_type' => Constant::RECORD_REFER_TYPE_CONTACT,
                        'stage' => $endStage,
                    ];

                    (new AiSdrTaskRecordBuilder($this->clientId, $task_id))->build($detail->id, $lead_id, Constant::RECORD_TYPE_DELIVERY_SUCCESS, $recordData);

                    $batchDetail->getOperator()->update(['delivery_status' => Constant::DETAIL_DELIVERY_STATUS_DELIVERY_SUCCESS]);
                    return;
                }
            }

            if ($source == Constant::TASK_SOURCE_IMPORT && $is_automatic_conversion) {
                $formData = [
                    'company' => $lead,
                    'customers' => $customers
                ];

                $form = new CustomerInputForm($this->clientId, $this->userId, PrivilegeConstants::FUNCTIONAL_CUSTOMER);
                $form->setExtraParams([
                    'lead_id' => $lead_id
                ]);
                $form->setData($formData);
                $company_id = $form->submit();

                $recordData = [
                    'refer_id' => $company_id,
                    'refer_type' => Constant::RECORD_REFER_TYPE_CONTACT,
                    'stage' => $endStage,
                ];
                (new AiSdrTaskRecordBuilder($this->clientId, $task_id))->build($detail->id, $lead_id, Constant::RECORD_TYPE_DELIVERY_SUCCESS, $recordData);

                $batchDetail->getOperator()->update(['delivery_status' => Constant::DETAIL_DELIVERY_STATUS_DELIVERY_SUCCESS]);
            }
        }
    }

    protected function needDownloadLogo($companyInfo) : false|string {
        if (!$this->task->isDig()) {
            return false;
        }
        if (empty($companyInfo['company_hash_id']) || !empty($companyInfo['image_list'])) {
            return false;
        }
        // 自动补充公司 logo
        $company = new Company($this->clientId, $this->userId);
        $companyInfo = $company->getCompanyInfo($companyInfo['company_hash_id']);
        if (!isset($companyInfo['logoUrl'])) {
            return false;
        }
        // 智能获客默认logo不进行下载
//        if (str_starts_with($companyInfo['logoUrl'], 'https://logo.clearbit.com/')) {
//            return false;
//        }
        return $companyInfo['logoUrl'];
    }
}