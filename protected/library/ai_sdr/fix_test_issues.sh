#!/bin/bash

# AI SDR 测试问题修复脚本
# 用于修复当前测试中发现的关键问题

set -e

# 配置变量
TESTS_DIR="/Users/<USER>/dev/xiaoman/crm/protected/tests"
PHPUNIT_BIN="/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit"
AI_SDR_DIR="/Users/<USER>/dev/xiaoman/crm/protected/library/ai_sdr"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_message() {
    echo -e "${1}${2}${NC}"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# 检查环境
check_environment() {
    print_header "检查测试环境"
    
    # 检查PHPUnit是否可用
    if [ ! -f "/Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit" ]; then
        print_message $RED "错误: PHPUnit不存在，请检查路径"
        exit 1
    fi
    
    # 检查测试目录
    if [ ! -d "$TESTS_DIR" ]; then
        print_message $RED "错误: 测试目录不存在"
        exit 1
    fi
    
    print_message $GREEN "环境检查通过"
}

# 运行基础测试验证
run_basic_tests() {
    print_header "运行基础测试验证"
    
    cd "$TESTS_DIR"
    
    # 运行单元测试
    print_message $BLUE "运行AI SDR单元测试..."
    $PHPUNIT_BIN --configuration phpunit.xml --testsuite ai_sdr_unit --verbose || {
        print_message $YELLOW "单元测试有部分失败，继续执行..."
    }
    
    # 运行基础功能测试
    print_message $BLUE "运行基础功能测试..."
    $PHPUNIT_BIN --configuration phpunit.xml --filter "AiSdrBasicFunctionalTest" functional/ai_sdr/ --verbose || {
        print_message $YELLOW "基础功能测试有部分失败，继续执行..."
    }
}

# 检查数据库结构
check_database_structure() {
    print_header "检查数据库结构"
    
    cd "$TESTS_DIR"
    
    # 运行数据库结构测试
    print_message $BLUE "验证数据库表结构..."
    $PHPUNIT_BIN --configuration phpunit.xml --filter "testAiSdrTaskTableStructure" integration/ai_sdr/ --verbose || {
        print_message $YELLOW "数据库结构测试有问题，需要检查配置"
    }
}

# 生成测试数据脚本
generate_test_data_script() {
    print_header "生成测试数据脚本"
    
    cat > "$AI_SDR_DIR/create_test_data.php" << 'EOF'
<?php
/**
 * AI SDR 测试数据创建脚本
 * 用于创建完整的测试数据集
 */

require_once dirname(__FILE__) . '/../../bootstrap.php';

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\profile\ClientProfile;

class AiSdrTestDataCreator
{
    private $testClientId = 2642; // 使用真实的测试客户ID
    private $testUserId = 249519530; // 使用真实的测试用户ID
    
    public function createTestData()
    {
        echo "开始创建AI SDR测试数据...\n";
        
        try {
            // 1. 创建测试任务
            $this->createTestTask();
            
            // 2. 创建测试详情
            $this->createTestDetails();
            
            // 3. 验证数据创建
            $this->verifyTestData();
            
            echo "测试数据创建完成!\n";
            
        } catch (Exception $e) {
            echo "创建测试数据失败: " . $e->getMessage() . "\n";
        }
    }
    
    private function createTestTask()
    {
        echo "创建测试任务...\n";
        
        $task = new AiSdrTask($this->testClientId);
        $task->client_id = $this->testClientId;
        $task->user_id = $this->testUserId;
        $task->source = Constant::TASK_SOURCE_AI_SDR;
        $task->current_stage = Constant::AI_SDR_STAGE_DIG;
        $task->end_stage = Constant::AI_SDR_STAGE_EFFECTIVE;
        $task->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
        $task->email = '<EMAIL>';
        $task->tags = [1, 2, 3];
        $task->stat_total = 0;
        $task->enable_flag = 1;
        
        if ($task->create()) {
            echo "测试任务创建成功，ID: " . $task->task_id . "\n";
            return $task->task_id;
        } else {
            throw new Exception("测试任务创建失败");
        }
    }
    
    private function createTestDetails()
    {
        echo "创建测试详情...\n";
        
        // 这里可以添加测试详情创建逻辑
        echo "测试详情创建完成\n";
    }
    
    private function verifyTestData()
    {
        echo "验证测试数据...\n";
        
        // 验证任务是否存在
        $taskFilter = new \common\library\ai_sdr\task\AiSdrTaskFilter($this->testClientId);
        $taskFilter->source = Constant::TASK_SOURCE_AI_SDR;
        $taskFilter->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
        $count = $taskFilter->count();
        
        echo "找到 {$count} 个测试任务\n";
    }
}

// 执行测试数据创建
$creator = new AiSdrTestDataCreator();
$creator->createTestData();
EOF

    print_message $GREEN "测试数据脚本已生成: $AI_SDR_DIR/create_test_data.php"
}

# 创建测试配置文件
create_test_config() {
    print_header "创建测试配置文件"
    
    cat > "$AI_SDR_DIR/test_config.json" << 'EOF'
{
    "test_environment": {
        "client_id": 2642,
        "user_id": 249519530,
        "database": {
            "host": "localhost",
            "port": 5432,
            "database": "crm_test"
        },
        "redis": {
            "host": "localhost",
            "port": 6379
        }
    },
    "test_data": {
        "tasks": {
            "dig_task": {
                "source": 1,
                "current_stage": 1,
                "end_stage": 5,
                "task_status": 1
            }
        },
        "external_services": {
            "recommend_api": {
                "mock_mode": true,
                "timeout": 30
            },
            "ai_services": {
                "mock_mode": true,
                "timeout": 60
            }
        }
    }
}
EOF

    print_message $GREEN "测试配置文件已创建: $AI_SDR_DIR/test_config.json"
}

# 生成测试报告
generate_test_report() {
    print_header "生成测试报告"
    
    cd "$TESTS_DIR"
    
    # 运行完整测试并生成报告
    print_message $BLUE "运行完整测试套件..."
    
    REPORT_FILE="$AI_SDR_DIR/test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "AI SDR 测试执行报告"
        echo "生成时间: $(date)"
        echo "================================"
        echo ""
        
        echo "单元测试结果:"
        echo "------------"
        $PHPUNIT_BIN --configuration phpunit.xml --testsuite ai_sdr_unit 2>&1 || true
        echo ""
        
        echo "功能测试结果:"
        echo "------------"
        $PHPUNIT_BIN --configuration phpunit.xml --filter "AiSdrBasicFunctionalTest" functional/ai_sdr/ 2>&1 || true
        echo ""
        
        echo "数据库测试结果:"
        echo "-------------"
        $PHPUNIT_BIN --configuration phpunit.xml --filter "RealDatabaseOperationTest" functional/ai_sdr/ 2>&1 || true
        
    } > "$REPORT_FILE"
    
    print_message $GREEN "测试报告已生成: $REPORT_FILE"
}

# 提供修复建议
provide_fix_suggestions() {
    print_header "修复建议"
    
    cat << 'EOF'
基于测试结果，以下是主要的修复建议:

1. 数据库配置问题:
   - 确保测试客户ID (2642) 在数据库中存在
   - 检查数据库连接配置
   - 验证所有AI SDR相关表的存在性

2. 业务数据完整性:
   - 运行测试数据创建脚本: php create_test_data.php
   - 创建完整的客户档案数据
   - 建立标准的测试场景

3. 外部服务集成:
   - 配置Mock服务的完整响应
   - 验证真实服务的连接配置
   - 实现服务健康检查机制

4. 测试环境优化:
   - 使用独立的测试数据库
   - 配置Redis测试实例
   - 建立测试数据的自动清理机制

下一步行动:
1. 运行: php create_test_data.php
2. 检查数据库连接配置
3. 重新运行测试套件
4. 根据结果进一步优化
EOF
}

# 主执行流程
main() {
    print_header "AI SDR 测试问题修复工具"
    
    check_environment
    run_basic_tests
    check_database_structure
    generate_test_data_script
    create_test_config
    generate_test_report
    provide_fix_suggestions
    
    print_message $GREEN "\n修复脚本执行完成!"
    print_message $BLUE "请查看生成的文件和建议，然后执行相应的修复步骤。"
}

# 执行主流程
main "$@"
