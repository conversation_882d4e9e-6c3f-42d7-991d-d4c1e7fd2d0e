AI SDR 测试执行报告
生成时间: Fri Jun  6 12:06:08 CST 2025
================================

单元测试结果:
------------
PHP Warning:  file_get_contents(ai_sdr_unit.php): Failed to open stream: No such file or directory in /Users/<USER>/dev/xiaoman/crm/protected/tests/vendor/antecedent/patchwork/src/CodeManipulation.php on line 122

Warning: file_get_contents(ai_sdr_unit.php): Failed to open stream: No such file or directory in /Users/<USER>/dev/xiaoman/crm/protected/tests/vendor/antecedent/patchwork/src/CodeManipulation.php on line 122
PHPUnit 9.5.10 by <PERSON> and contributors.

Warning:       Your XML configuration validates against a deprecated schema.
Suggestion:    Migrate your XML configuration using "--migrate-configuration"!

..S...S....SS..SS........................S..............S.....    62 / 62 (100%)

Time: 00:04.620, Memory: 70.03 MB

OK, but incomplete, skipped, or risky tests!
Tests: 62, Assertions: 488, Skipped: 8.

功能测试结果:
------------
PHPUnit 9.5.10 by Sebastian Bergmann and contributors.

Warning:       Your XML configuration validates against a deprecated schema.
Suggestion:    Migrate your XML configuration using "--migrate-configuration"!

............                                                      12 / 12 (100%)

Time: 00:04.042, Memory: 70.03 MB

OK (12 tests, 82 assertions)

数据库测试结果:
-------------
PHPUnit 9.5.10 by Sebastian Bergmann and contributors.

Warning:       Your XML configuration validates against a deprecated schema.
Suggestion:    Migrate your XML configuration using "--migrate-configuration"!

.S.S..                                                              6 / 6 (100%)

Time: 00:10.142, Memory: 48.00 MB

OK, but incomplete, skipped, or risky tests!
Tests: 6, Assertions: 46, Skipped: 2.
