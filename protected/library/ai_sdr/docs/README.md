# AI SDR 系统设计文档

## 概述

AI SDR (Sales Development Representative) 是一个智能销售开发系统，通过AI技术自动化潜客挖掘、背景调研、质量评估和营销触达等销售流程。

## 系统架构

### 核心设计模式

AI SDR系统采用以下设计模式：

1. **领域驱动设计 (DDD)** - 按业务领域划分模块
2. **状态机模式** - 使用Symfony Workflow管理潜客状态流转
3. **ORM模式** - 基于xiaoman/orm框架进行数据访问
4. **事件驱动** - 通过队列和事件处理异步任务
5. **策略模式** - 不同场景下的处理策略

### 目录结构

```
protected/library/ai_sdr/
├── AISdrService.php              # 核心服务类
├── SdrDetailExecutor.php         # 状态机执行器
├── Constant.php                  # 常量定义
├── Helper.php                    # 工具类
├── config/
│   └── workflow.yaml            # 状态机配置
├── task/                        # 任务管理
├── task_detail/                 # 任务详情
├── task_record/                 # 任务记录
├── message/                     # 消息系统
├── buyer_portrait/              # 买家画像
├── profile/                     # 客户档案
├── dig_record/                  # 挖掘记录
├── usage_record/                # 使用记录
├── jobs/                        # 异步任务
└── statistic/                   # 统计分析
```

## 核心模块

### 1. 任务管理 (Task)

**核心类：**
- `AiSdrTask` - 任务实体
- `AiSdrTaskFilter` - 任务查询过滤器
- `AiSdrTaskFormatter` - 任务数据格式化
- `AiSdrTaskOperator` - 任务操作器

**主要功能：**
- 任务创建和配置
- 任务状态管理
- 任务执行调度
- 任务统计和监控

**任务状态：**
```php
const AI_SDR_TASK_STATUS_DRAFT = 0;      // 草稿
const AI_SDR_TASK_STATUS_PROCESSING = 1; // 处理中
const AI_SDR_TASK_STATUS_PAUSED = 2;     // 暂停
const AI_SDR_TASK_STATUS_FINISHED = 3;   // 已完成
```

**任务阶段：**
```php
const AI_SDR_STAGE_DIG = 0;        // 挖掘阶段
const AI_SDR_STAGE_REACHABLE = 1;  // 可触达阶段
const AI_SDR_STAGE_MARKETING = 2;  // 营销阶段
const AI_SDR_STAGE_EFFECTIVE = 3;  // 有效阶段
const AI_SDR_STAGE_HIGHVALUE = 4;  // 高价值阶段
```

### 2. 任务详情 (TaskDetail)

**核心类：**
- `AiSdrTaskDetail` - 任务详情实体
- `SdrLeadDetail` - 潜客详情包装类

**主要功能：**
- 单个潜客的处理状态
- 潜客质量评估
- 联系人信息管理
- 营销记录跟踪

### 3. 状态机执行器 (SdrDetailExecutor)

**核心功能：**
- 基于Symfony Workflow的状态流转
- 状态转换验证和执行
- AI服务集成调用
- 异常处理和重试

**状态流转图：**
```
添加(0) → 标签(1) → 背调(2) → 校验联系人(4) → 营销计划(5) → 执行营销(6) → 有效(7) → 高价值(8)
    ↓         ↓        ↓           ↓            ↓           ↓          ↓         ↓
                                                                    错误(-1)
```

### 4. 消息系统 (Message)

**核心类：**
- `Message` - 消息实体
- `MessageService` - 消息服务

**消息类型：**
- 工作汇报
- 操作指南
- 任务暂停通知
- 筛选提示
- 资产盘活通知

## 业务流程

### 1. 任务创建流程

1. **任务配置** - 设置目标阶段、邮箱、标签等
2. **客户档案** - 获取或创建客户档案
3. **任务启动** - 状态变更为处理中
4. **队列调度** - 加入处理队列

### 2. 潜客处理流程

#### 挖掘阶段 (DIG)
1. **推荐获取** - 调用推荐API获取潜在客户
2. **质量评估** - AI评估潜客质量
3. **线索创建** - 符合条件的创建线索
4. **背景调研** - 启动AI背调服务

#### 可触达阶段 (REACHABLE)
1. **背调完成** - 获取背调报告
2. **联系人校验** - 验证邮箱有效性
3. **联系人补充** - 添加关键联系人
4. **标签添加** - 根据质量添加标签

#### 营销阶段 (MARKETING)
1. **营销计划** - AI生成营销内容
2. **EDM创建** - 创建邮件营销任务
3. **执行营销** - 按计划发送邮件
4. **效果跟踪** - 监控营销效果

### 3. 状态机配置

状态机配置文件：`config/workflow.yaml`

```yaml
type: workflow
places: [0, 1, 2, 4, 5, 6, 7, 8, -1]
transitions:
  label_lead_quality:
    from: [0,2]
    to: 1
  start_background_check:
    from: [0,1,2]
    to: 2
  validate_contact:
    from: [1,2]
    to: 4
  start_marketing:
    from: 4
    to: 5
  execute_marketing:
    from: [5,6]
    to: 6
```

## ORM框架使用

### 基础模式

所有实体类继承自xiaoman/orm框架：

```php
class AiSdrTask extends SingleObject
{
    use InitAiSdrTaskMetadata;
    
    public static function getMetadataClass()
    {
        return AiSdrTaskMetadata::class;
    }
}
```

### 查询模式

使用Filter类进行复杂查询：

```php
$filter = new AiSdrTaskFilter($clientId);
$filter->source = new NotEqual(Constant::TASK_SOURCE_SYSTEM);
$filter->task_status = new NotEqual(Constant::AI_SDR_TASK_STATUS_PAUSED);
$filter->order('create_time', 'desc');
$tasks = $filter->rawData();
```

### 批量操作

使用Batch类进行批量操作：

```php
$batchDetail = new BatchAiSdrTaskDetail($clientId);
$batchDetail->initFromData($detailData);
$batchDetail->getOperator()->update(['status' => $newStatus]);
```

## AI服务集成

### 1. 潜客质量分析

```php
$agent = new SdrLeadQualityAnalysisAgent($clientId, $userId);
$result = $agent->process([
    'client_profile' => json_encode($clientProfile),
    'buyer_profile' => json_encode($buyerProfile)
]);
```

### 2. 营销内容生成

```php
$agent = new SdrEdmWriteAgent($clientId, $userId);
$emailContents = $agent->process([
    'client_profile' => json_encode($clientProfile),
    'buyer_profile' => json_encode($buyerProfile)
]);
```

### 3. 背景调研

```php
$aiBackgroundService = new AiBackgroundService();
$result = $aiBackgroundService->createTask(
    $clientId, 
    $userId, 
    $referType, 
    $detailId, 
    $params
);
```

## 队列和异步处理

### 主要Job类

- `AiSdrProcessTaskJob` - 任务处理
- `AiSdrDigTaskJob` - 挖掘任务
- `AiSdrLeadQualityAnalyzeJob` - 质量分析
- `AiSdrCrmBuyerPortraitJob` - 买家画像

### 队列调度

```php
$job = new AiSdrProcessTaskJob($clientId, $taskId);
QueueService::dispatch($job);
```

## 配置和常量

### 限制配置

```php
const DAILY_LIMIT = 70;                    // 每日处理限制
const TOTAL_LIMIT_UNLIMITED = -1;          // 无限制
const AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT = 10000; // 潜客数量限制
```

### 缓存键

```php
const REDIS_CACHE_TASK_KEY = "sdr:task:{%s}:{%s}";
const TASK_DAILY_LIMIT_CACHE_KEY = 'sdr:task:limit:{%s}:{%s}';
```

## 扩展指南

### 1. 添加新的处理阶段

1. 在`Constant.php`中定义新阶段常量
2. 更新`workflow.yaml`配置
3. 在`SdrDetailExecutor`中添加处理逻辑
4. 更新相关Formatter和Filter

### 2. 添加新的AI服务

1. 创建新的Agent类
2. 在`SdrDetailExecutor`中集成调用
3. 添加相应的记录类型
4. 更新错误处理逻辑

### 3. 扩展消息类型

1. 在`Constant.php`中定义消息类型
2. 创建对应的消息扩展类
3. 在相应业务逻辑中触发消息

## 监控和调试

### 日志记录

系统使用`\LogUtil::info()`记录关键操作：

```php
\LogUtil::info("client_id {$clientId} task {$taskId} process stage {$stage}");
```

### Redis锁机制

防止并发处理：

```php
$key = sprintf(Constant::REDIS_CACHE_TASK_KEY, $clientId, $taskId);
$result = $redis->set($key, 1, 'EX', 3600, 'NX');
```

### 错误处理

状态机中的错误会将详情状态设置为`DETAIL_STATUS_ERROR(-1)`。

## 性能优化

### 1. 批量处理

- 使用BatchAiSdrTaskDetail进行批量更新
- 分块处理大量数据

### 2. 缓存策略

- Redis缓存任务状态
- 限流控制

### 3. 异步处理

- 耗时操作通过队列异步执行
- AI服务调用异步化

## 安全考虑

### 1. 权限控制

- 基于client_id的数据隔离
- 功能权限验证

### 2. 数据验证

- 输入参数验证
- 业务规则校验

### 3. 异常处理

- 完善的try-catch机制
- 优雅降级处理

## 数据库设计

### 核心表结构

#### ai_sdr_task (任务表)
- `task_id` - 任务ID (主键)
- `client_id` - 客户ID
- `user_id` - 用户ID
- `source` - 任务来源 (AI_SDR/IMPORT/CRM_EP)
- `current_stage` - 当前阶段
- `end_stage` - 目标阶段
- `task_status` - 任务状态
- `email` - 营销邮箱
- `tags` - 标签数组
- `stat_total` - 统计总数
- `create_time` - 创建时间
- `update_time` - 更新时间

#### ai_sdr_task_detail (任务详情表)
- `id` - 详情ID (主键)
- `task_id` - 任务ID
- `lead_id` - 线索ID
- `user_id` - 用户ID
- `source` - 来源
- `stage` - 当前阶段
- `status` - 处理状态
- `lead_quality` - 潜客质量
- `product_ids` - 产品ID数组
- `company_types` - 公司类型数组
- `enable_flag` - 启用标志
- `stage_dig_time` - 挖掘阶段时间
- `stage_reachable_time` - 可触达阶段时间
- `stage_marketing_time` - 营销阶段时间
- `stage_effective_time` - 有效阶段时间
- `stage_highvalue_time` - 高价值阶段时间

#### ai_sdr_task_record (任务记录表)
- `record_id` - 记录ID (主键)
- `task_id` - 任务ID
- `detail_id` - 详情ID
- `lead_id` - 线索ID
- `type` - 记录类型
- `data` - 记录数据 (JSON)
- `estimate_time` - 预计时间
- `executed_time` - 执行时间
- `refer_type` - 关联类型
- `refer_id` - 关联ID

### 索引设计

```sql
-- 任务表索引
CREATE INDEX idx_ai_sdr_task_client_status ON ai_sdr_task(client_id, task_status);
CREATE INDEX idx_ai_sdr_task_source_stage ON ai_sdr_task(source, current_stage);

-- 详情表索引
CREATE INDEX idx_ai_sdr_task_detail_task_status ON ai_sdr_task_detail(task_id, status);
CREATE INDEX idx_ai_sdr_task_detail_lead_stage ON ai_sdr_task_detail(lead_id, stage);
CREATE INDEX idx_ai_sdr_task_detail_quality ON ai_sdr_task_detail(lead_quality, enable_flag);

-- 记录表索引
CREATE INDEX idx_ai_sdr_task_record_detail_type ON ai_sdr_task_record(detail_id, type);
CREATE INDEX idx_ai_sdr_task_record_task_time ON ai_sdr_task_record(task_id, estimate_time);
```

## API接口设计

### 1. 任务管理接口

#### 创建任务
```php
POST /api/ai-sdr/task/create
{
    "source": 1,
    "end_stage": 2,
    "email": "<EMAIL>",
    "tags": ["tag1", "tag2"],
    "filters": {...}
}
```

#### 获取任务列表
```php
GET /api/ai-sdr/task/list
Response: {
    "tasks": [
        {
            "task_id": 123,
            "source": 1,
            "current_stage": 1,
            "end_stage": 2,
            "task_status": 1,
            "create_time": "2024-01-01 10:00:00"
        }
    ]
}
```

#### 暂停/恢复任务
```php
POST /api/ai-sdr/task/{taskId}/pause
POST /api/ai-sdr/task/{taskId}/resume
```

### 2. 潜客管理接口

#### 获取潜客列表
```php
GET /api/ai-sdr/task/{taskId}/leads
Parameters:
- stage: 阶段筛选
- status: 状态筛选
- quality: 质量筛选
- page: 页码
- limit: 每页数量
```

#### 潜客详情
```php
GET /api/ai-sdr/lead/{leadId}/detail
Response: {
    "lead_info": {...},
    "buyer_profile": {...},
    "marketing_records": [...],
    "quality_analysis": {...}
}
```

### 3. 统计分析接口

#### 任务统计
```php
GET /api/ai-sdr/task/{taskId}/statistics
Response: {
    "total_leads": 1000,
    "stage_distribution": {
        "dig": 300,
        "reachable": 400,
        "marketing": 200,
        "effective": 80,
        "high_value": 20
    },
    "quality_distribution": {
        "high": 100,
        "medium": 300,
        "low": 600
    }
}
```

## 测试指南

### 单元测试

#### 测试任务创建
```php
public function testCreateTask()
{
    $service = new AISdrService($this->clientId, $this->userId);
    $task = $service->createTask([
        'source' => Constant::TASK_SOURCE_AI_SDR,
        'end_stage' => Constant::AI_SDR_STAGE_MARKETING,
        'email' => '<EMAIL>'
    ]);

    $this->assertNotNull($task->task_id);
    $this->assertEquals(Constant::AI_SDR_TASK_STATUS_DRAFT, $task->task_status);
}
```

#### 测试状态机流转
```php
public function testWorkflowTransition()
{
    $executor = new SdrDetailExecutor($this->clientId, $this->userId);
    $detail = new SdrLeadDetail([...]);

    $result = $executor->process([$detail], Constant::DETAIL_STATUS_LABEL);
    $this->assertTrue($result);
}
```

### 集成测试

#### 测试完整流程
```php
public function testFullWorkflow()
{
    // 1. 创建任务
    $task = $this->createTestTask();

    // 2. 启动处理
    $service = new AISdrService($this->clientId, $this->userId);
    $service->processTask($task->task_id);

    // 3. 验证结果
    $this->assertTaskProcessed($task->task_id);
}
```

### 性能测试

#### 批量处理测试
```php
public function testBatchProcessing()
{
    $startTime = microtime(true);

    // 处理1000个潜客
    $service = new AISdrService($this->clientId, $this->userId);
    $service->processBatchLeads($leadIds);

    $endTime = microtime(true);
    $this->assertLessThan(60, $endTime - $startTime); // 60秒内完成
}
```

## 部署和运维

### 1. 环境配置

#### 必需的PHP扩展
- Redis
- PostgreSQL
- cURL
- JSON

#### 队列配置
```php
// config/queue.php
'ai_sdr' => [
    'driver' => 'redis',
    'connection' => 'default',
    'queue' => 'ai_sdr_tasks',
    'retry_after' => 300,
    'block_for' => 5,
]
```

### 2. 监控指标

#### 关键指标
- 任务处理速度
- AI服务响应时间
- 错误率
- 队列积压情况

#### 告警规则
- 任务失败率 > 5%
- 队列积压 > 1000
- AI服务超时 > 30秒

### 3. 日常维护

#### 数据清理
```sql
-- 清理30天前的记录
DELETE FROM ai_sdr_task_record
WHERE create_time < NOW() - INTERVAL '30 days';
```

#### 性能优化
- 定期分析慢查询
- 优化索引使用
- 清理无效缓存

## 故障排查

### 常见问题

#### 1. 任务卡住不处理
**原因：** Redis锁未释放
**解决：** 检查并清理Redis锁
```bash
redis-cli DEL "sdr:task:{client_id}:{task_id}"
```

#### 2. AI服务调用失败
**原因：** 网络超时或服务异常
**解决：** 检查AI服务状态，重试机制

#### 3. 状态机流转异常
**原因：** 状态验证失败
**解决：** 检查workflow.yaml配置和验证逻辑

### 调试工具

#### 日志查看
```bash
tail -f /var/log/ai_sdr.log | grep "task_id:{task_id}"
```

#### Redis监控
```bash
redis-cli MONITOR | grep "sdr:"
```

#### 数据库查询
```sql
-- 查看任务处理状态
SELECT task_id, current_stage, task_status, update_time
FROM ai_sdr_task
WHERE client_id = ? AND task_status = 1;

-- 查看详情处理进度
SELECT stage, status, COUNT(*)
FROM ai_sdr_task_detail
WHERE task_id = ?
GROUP BY stage, status;
```

## 代码示例

### 1. 创建自定义处理器

```php
<?php
namespace common\library\ai_sdr\processors;

use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\SdrLeadDetail;

class CustomProcessor
{
    protected $clientId;
    protected $userId;

    public function __construct(int $clientId, int $userId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
    }

    public function processCustomLogic(array $details): array
    {
        $executor = new SdrDetailExecutor($this->clientId, $this->userId);
        $results = [];

        foreach ($details as $detail) {
            try {
                // 自定义处理逻辑
                $result = $this->handleDetail($detail);
                $results[] = $result;
            } catch (\Exception $e) {
                \LogUtil::info("Custom processing failed", [
                    'detail_id' => $detail->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    private function handleDetail(SdrLeadDetail $detail)
    {
        // 实现具体的处理逻辑
        return $detail;
    }
}
```

### 2. 扩展消息类型

```php
<?php
namespace common\library\ai_sdr\message\message_extends;

use common\library\ai_sdr\message\Message;
use common\library\ai_sdr\Constant;

class CustomNotification extends Message
{
    public function __construct(int $clientId, int $taskId)
    {
        parent::__construct($clientId);
        $this->task_id = $taskId;
        $this->type = Constant::SDR_MESSAGE_TYPE_CUSTOM; // 需要在Constant中定义
    }

    public function setCustomData(array $data): self
    {
        $this->data = [
            'title' => $data['title'] ?? '',
            'content' => $data['content'] ?? '',
            'action_url' => $data['action_url'] ?? '',
            'timestamp' => xm_function_now()
        ];
        return $this;
    }

    protected function formatMessage(): array
    {
        return [
            'type' => $this->type,
            'data' => $this->data,
            'create_time' => $this->create_time
        ];
    }
}
```

### 3. 自定义AI Agent集成

```php
<?php
namespace common\library\ai_sdr\agents;

use common\library\ai_agent\BaseAgent;

class CustomAnalysisAgent extends BaseAgent
{
    protected function getPrompt(): string
    {
        return "分析客户数据并提供建议...";
    }

    protected function validateInput(array $input): bool
    {
        return isset($input['client_data']) && isset($input['analysis_type']);
    }

    protected function processResponse(array $response): array
    {
        return [
            'analysis_result' => $response['result'] ?? [],
            'confidence_score' => $response['confidence'] ?? 0.0,
            'recommendations' => $response['recommendations'] ?? []
        ];
    }

    public function analyzeClient(array $clientData, string $analysisType): array
    {
        return $this->process([
            'client_data' => $clientData,
            'analysis_type' => $analysisType
        ]);
    }
}
```

### 4. 批量操作优化

```php
<?php
namespace common\library\ai_sdr\optimizers;

use common\library\ai_sdr\task_detail\BatchAiSdrTaskDetail;

class BatchOptimizer
{
    protected $clientId;
    protected $batchSize = 100;

    public function __construct(int $clientId)
    {
        $this->clientId = $clientId;
    }

    public function batchUpdateDetails(array $updates): bool
    {
        $chunks = array_chunk($updates, $this->batchSize, true);
        $batch = new BatchAiSdrTaskDetail($this->clientId);

        foreach ($chunks as $chunk) {
            try {
                $batch->getOperator()->batchUpdate($chunk);
                \LogUtil::info("Batch update completed", [
                    'client_id' => $this->clientId,
                    'count' => count($chunk)
                ]);
            } catch (\Exception $e) {
                \LogUtil::info("Batch update failed", [
                    'client_id' => $this->clientId,
                    'error' => $e->getMessage(),
                    'chunk_size' => count($chunk)
                ]);
                return false;
            }
        }

        return true;
    }

    public function optimizeQuery(callable $queryBuilder): array
    {
        // 查询优化逻辑
        $startTime = microtime(true);
        $result = $queryBuilder();
        $endTime = microtime(true);

        \LogUtil::info("Query performance", [
            'execution_time' => $endTime - $startTime,
            'result_count' => count($result)
        ]);

        return $result;
    }
}
```

## 最佳实践

### 1. 错误处理

```php
// 推荐的错误处理模式
try {
    $result = $this->processRiskyOperation();
} catch (AiAgentException $e) {
    // AI服务特定错误
    \LogUtil::info("AI service error", [
        'operation' => 'processRiskyOperation',
        'error' => $e->getMessage(),
        'context' => $e->getContext()
    ]);
    return $this->handleAiServiceError($e);
} catch (\Exception $e) {
    // 通用错误
    \LogUtil::info("General error", [
        'operation' => 'processRiskyOperation',
        'error' => $e->getMessage()
    ]);
    return $this->handleGeneralError($e);
}
```

### 2. 缓存策略

```php
// Redis缓存最佳实践
class CacheManager
{
    private $redis;
    private $defaultTtl = 3600;

    public function getOrSet(string $key, callable $callback, int $ttl = null): mixed
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $cached = $this->redis->get($key);

        if ($cached !== false) {
            return json_decode($cached, true);
        }

        $data = $callback();
        $this->redis->setex($key, $ttl, json_encode($data));

        return $data;
    }

    public function invalidatePattern(string $pattern): void
    {
        $keys = $this->redis->keys($pattern);
        if (!empty($keys)) {
            $this->redis->del($keys);
        }
    }
}
```

### 3. 性能监控

```php
// 性能监控装饰器
class PerformanceMonitor
{
    public static function monitor(string $operation, callable $callback): mixed
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        try {
            $result = $callback();

            $endTime = microtime(true);
            $endMemory = memory_get_usage();

            \LogUtil::info("Performance metrics", [
                'operation' => $operation,
                'execution_time' => $endTime - $startTime,
                'memory_usage' => $endMemory - $startMemory,
                'peak_memory' => memory_get_peak_usage()
            ]);

            return $result;
        } catch (\Exception $e) {
            \LogUtil::info("Operation failed", [
                'operation' => $operation,
                'error' => $e->getMessage(),
                'execution_time' => microtime(true) - $startTime
            ]);
            throw $e;
        }
    }
}
```

### 4. 数据验证

```php
// 数据验证器
class DataValidator
{
    public static function validateTaskData(array $data): array
    {
        $errors = [];

        if (empty($data['source']) || !in_array($data['source'], [1, 2, 3])) {
            $errors[] = 'Invalid task source';
        }

        if (empty($data['end_stage']) || $data['end_stage'] < 0 || $data['end_stage'] > 4) {
            $errors[] = 'Invalid end stage';
        }

        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }

        return $errors;
    }

    public static function sanitizeInput(array $input): array
    {
        return array_map(function($value) {
            if (is_string($value)) {
                return trim(strip_tags($value));
            }
            return $value;
        }, $input);
    }
}
```

## 版本更新指南

### 版本兼容性

#### v1.0 → v1.1 升级
1. 数据库迁移
```sql
ALTER TABLE ai_sdr_task_detail
ADD COLUMN stage_dig_time TIMESTAMP DEFAULT '1970-01-01 00:00:01';
```

2. 配置更新
```php
// 新增配置项
const TASK_DETAIL_STAGE_TIME_MAP = [
    self::AI_SDR_STAGE_DIG => 'stage_dig_time',
    // ...
];
```

3. 代码兼容性检查
```php
// 检查新字段是否存在
if (property_exists($detail, 'stage_dig_time')) {
    // 使用新字段
} else {
    // 兼容旧版本
}
```

### 迁移脚本

```php
<?php
// migration/ai_sdr_v1_1.php
class AiSdrV11Migration
{
    public function up()
    {
        // 添加新字段
        $this->addColumn('ai_sdr_task_detail', 'stage_dig_time', 'timestamp');

        // 更新现有数据
        $this->updateExistingData();

        // 创建新索引
        $this->createIndex('ai_sdr_task_detail', 'stage_dig_time');
    }

    public function down()
    {
        // 回滚操作
        $this->dropColumn('ai_sdr_task_detail', 'stage_dig_time');
    }

    private function updateExistingData()
    {
        // 批量更新现有记录的时间字段
    }
}
```

## 总结

AI SDR系统是一个复杂的智能销售自动化平台，采用了现代化的架构设计和最佳实践：

1. **模块化设计** - 清晰的职责分离和可扩展性
2. **状态机管理** - 可靠的业务流程控制
3. **异步处理** - 高性能的任务处理能力
4. **AI集成** - 智能化的决策和内容生成
5. **监控完善** - 全面的日志和性能监控

通过遵循本文档的设计原则和最佳实践，可以有效地维护和扩展AI SDR系统，确保系统的稳定性和可靠性。
