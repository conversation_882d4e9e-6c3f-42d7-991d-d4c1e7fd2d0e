# CRM 模块完整测试指南

## 📋 概述

本指南提供CRM系统模块测试的通用方法和最佳实践，以AI SDR功能作为具体示例，涵盖单元测试、功能测试、集成测试和端到端测试的完整流程。适用于所有CRM模块的测试开发。

## 🏗️ 通用测试架构

### 测试层次结构 (以AI SDR为例)
```
CRM 模块测试体系
├── 单元测试 (Unit Tests)
│   ├── 核心服务测试 (如: AISdrServiceTest)
│   ├── 业务逻辑测试 (如: SdrDetailExecutorTest)
│   ├── 数据模型测试 (如: DataModelTest)
│   └── 工具类测试 (如: ConstantAndHelperTest)
├── 功能测试 (Functional Tests)
│   ├── API接口测试 (如: AiSdrBasicFunctionalTest)
│   ├── 业务流程测试 (如: AiSdrBusinessFlowTest)
│   └── 依赖注入测试 (如: ServiceDependencyInjectionTest)
├── 集成测试 (Integration Tests)
│   ├── 数据库集成测试 (如: DatabaseStructureTest)
│   ├── 外部服务集成测试 (如: ExternalServiceIntegrationTest)
│   └── 端到端测试 (如: CompleteWorkflowTest)
└── 性能测试 (Performance Tests)
    ├── 并发控制测试 (如: ConcurrencyControlTest)
    └── 性能基准测试 (如: PerformanceTest)
```

### 适用模块范围
- **业务模块**: 客户管理、线索管理、机会管理、订单管理等
- **功能模块**: AI服务、推荐系统、营销自动化、报表统计等
- **基础模块**: 用户权限、消息通知、文件管理、系统配置等

### 通用测试目录结构 (以AI SDR为例)
```
protected/tests/
├── unit/{module_name}/             # 单元测试目录
│   ├── {Module}ServiceTest.php    # 核心服务测试
│   ├── {Module}ExecutorTest.php   # 业务逻辑测试
│   ├── DataModelTest.php          # 数据模型测试
│   ├── ConstantAndHelperTest.php  # 工具类测试
│   ├── {Module}TestCase.php       # 模块测试基类
│   ├── {Module}TestDataFactory.php # 测试数据工厂
│   └── MockServices.php           # Mock服务集合
├── functional/{module_name}/       # 功能测试目录
│   ├── {Module}BasicFunctionalTest.php     # 基础功能测试
│   ├── {Module}BusinessFlowTest.php        # 业务流程测试
│   ├── {Module}DependencyInjectionTest.php # 依赖注入测试
│   └── RealDatabaseOperationTest.php       # 真实数据库操作测试
├── integration/{module_name}/      # 集成测试目录
│   ├── {Module}DatabaseStructureTest.php   # 数据库结构测试
│   ├── {Module}CompleteWorkflowTest.php    # 端到端测试
│   └── {Module}ExternalServiceTest.php     # 外部服务集成测试
└── {MODULE}_TESTING_GUIDE.md      # 模块测试指南
```

### 命名约定
- **测试类**: `{功能名}Test.php` (如: `AISdrServiceTest.php`)
- **测试方法**: `test{方法名}_{条件}_{预期结果}` (如: `testCreateTask_WithValidData_ReturnsTaskId`)
- **测试基类**: `{模块名}TestCase.php` (如: `AiSdrTestCase.php`)
- **数据工厂**: `{模块名}TestDataFactory.php` (如: `AiSdrTestDataFactory.php`)

## 🚀 快速开始

### 环境准备

1. **检查PHPUnit安装**
```bash
# 验证PHPUnit路径 (根据实际环境调整)
ls -la /path/to/external/vendor/phpunit/phpunit/phpunit
```

2. **配置测试环境**
```bash
# 进入测试目录
cd /path/to/crm/protected/tests

# 检查PHPUnit配置文件
cat phpunit.xml

# 确认测试套件配置
grep -A 5 "testsuite name" phpunit.xml
```

3. **准备模块测试数据 (以AI SDR为例)**
```bash
# 运行模块测试数据创建脚本
cd /path/to/crm
php protected/library/{module_name}/create_test_data.php

# 或使用通用测试数据工厂
php protected/tests/scripts/create_module_test_data.php --module=ai_sdr
```

### 基础测试执行模式

**运行模块单元测试 (以AI SDR为例)**
```bash
cd /path/to/crm/protected/tests
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --testsuite {module_name}_unit \
  --verbose

# 示例: AI SDR模块
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --testsuite ai_sdr_unit \
  --verbose
```

**运行模块功能测试**
```bash
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --testsuite {module_name}_functional \
  --verbose

# 示例: AI SDR功能测试
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --testsuite ai_sdr_functional \
  --verbose
```

**运行特定测试类**
```bash
# 通用模式
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter {TestClassName} \
  unit/{module_name}/{TestClassName}.php

# 示例: AI SDR服务测试
php /path/to/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter AISdrServiceTest \
  unit/ai_sdr/AISdrServiceTest.php
```

## 📝 通用测试编写指南

### 1. 单元测试编写模式

**通用测试类结构 (以AI SDR为例)**
```php
<?php
namespace tests\unit\{module_name};

use common\library\{module_name}\{Module}Service;
use tests\unit\{module_name}\{Module}TestCase;
use tests\unit\{module_name}\MockServices;

class {Module}ServiceTest extends {Module}TestCase
{
    protected $service;
    protected $mockServices;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建Mock服务集合
        $this->mockServices = MockServices::createAllMockServices();

        // 创建被测试的服务 (依赖注入模式)
        $this->service = new {Module}Service(
            self::TEST_CLIENT_ID,
            self::TEST_USER_ID,
            $this->mockServices['externalApi'],    // 外部API服务
            $this->mockServices['dataService'],    // 数据服务
            $this->mockServices['queueService']    // 队列服务
        );
    }

    public function testMethodName_WithValidInput_ReturnsExpectedResult()
    {
        // Arrange - 准备测试数据
        $input = 'test_input';

        // Act - 执行被测试的方法
        $result = $this->service->methodName($input);

        // Assert - 验证结果
        $this->assertEquals('expected_result', $result);
        $this->assertTrue($this->mockServices['externalApi']->wasMethodCalled('someMethod'));
    }
}

// AI SDR 具体示例
class AISdrServiceTest extends AiSdrTestCase
{
    // ... 具体实现参考上述模式
}
```

**通用测试原则**
- **命名规范**: `testMethodName_WithCondition_ExpectedBehavior`
- **AAA模式**: Arrange (准备), Act (执行), Assert (验证)
- **依赖隔离**: 使用Mock对象隔离外部依赖
- **完整验证**: 验证返回值、方法调用、状态变化
- **边界测试**: 覆盖正常、边界、异常情况
- **单一职责**: 每个测试方法只验证一个功能点

### 2. 功能测试编写模式

**通用功能测试结构 (以AI SDR为例)**
```php
<?php
namespace tests\functional\{module_name};

use WebFunctionalTestCase;

class {Module}FunctionalTest extends WebFunctionalTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01(); // 设置用户登录状态
    }

    public function testApiEndpoint_WithValidRequest_ReturnsSuccessResponse()
    {
        // 准备请求数据
        $requestData = [
            'id' => 12345,
            'action' => 'process',
            'params' => ['key' => 'value']
        ];

        // 发送API请求
        $response = $this->makeApiRequest('/{module_name}/endpoint', $requestData);

        // 验证响应结构
        $this->assertEquals(200, $response['status']);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('message', $response);
    }

    public function testBusinessFlow_WithCompleteData_ExecutesSuccessfully()
    {
        // 测试完整业务流程
        $step1Result = $this->executeBusinessStep1();
        $this->assertTrue($step1Result['success']);

        $step2Result = $this->executeBusinessStep2($step1Result['data']);
        $this->assertTrue($step2Result['success']);
    }
}

// AI SDR 具体示例
class AiSdrFunctionalTest extends WebFunctionalTestCase
{
    public function testCreateTask_WithValidData_ReturnsTaskId()
    {
        $response = $this->makeApiRequest('/ai_sdr/create_task', [
            'source' => 1,
            'end_stage' => 5,
            'email' => '<EMAIL>'
        ]);

        $this->assertEquals(200, $response['status']);
        $this->assertArrayHasKey('task_id', $response['data']);
    }
}
```

### 3. 集成测试编写模式

**通用数据库集成测试 (以AI SDR为例)**
```php
<?php
namespace tests\integration\{module_name};

use common\library\{module_name}\{Module}Model;
use tests\unit\{module_name}\{Module}TestCase;

class {Module}DatabaseIntegrationTest extends {Module}TestCase
{
    public function testDatabaseOperation_WithRealData_PersistsCorrectly()
    {
        try {
            // 创建真实的数据库记录
            $model = new {Module}Model(self::TEST_CLIENT_ID);
            $model->client_id = self::TEST_CLIENT_ID;
            $model->user_id = self::TEST_USER_ID;
            $model->status = 1;
            $model->data = ['key' => 'value'];
            // ... 设置其他必需字段

            $created = $model->create();
            $this->assertTrue($created, 'Model should be created successfully');

            if ($created) {
                // 记录测试数据ID用于清理
                self::$testDataIds['models'][] = $model->id;

                // 验证数据持久化
                $savedModel = new {Module}Model(self::TEST_CLIENT_ID, $model->id);
                $this->assertFalse($savedModel->isNew(), 'Model should exist in database');
                $this->assertEquals($model->status, $savedModel->status);
            }

        } catch (\Exception $e) {
            $this->markTestSkipped('Database operation failed: ' . $e->getMessage());
        }
    }

    public function testComplexQuery_WithJoins_ReturnsCorrectData()
    {
        // 测试复杂查询和关联
        $filter = new {Module}Filter(self::TEST_CLIENT_ID);
        $filter->status = 1;
        $filter->with(['relatedModel']);

        $results = $filter->rawData();
        $this->assertIsArray($results);

        if (!empty($results)) {
            $this->assertArrayHasKey('related_data', $results[0]);
        }
    }
}

// AI SDR 具体示例
class AiSdrDatabaseIntegrationTest extends AiSdrTestCase
{
    public function testTaskCreation_WithArrayFields_HandlesCorrectly()
    {
        $task = new AiSdrTask(self::TEST_CLIENT_ID);
        $task->tags = [1, 2, 3]; // 测试数组字段
        // ... 其他字段设置

        $created = $task->create();
        $this->assertTrue($created);

        $savedTask = new AiSdrTask(self::TEST_CLIENT_ID, $task->task_id);
        $this->assertEquals([1, 2, 3], $savedTask->tags);
    }
}
```

## 🔧 测试工具和辅助类

### 1. AiSdrTestCase 基类

**功能特性**
- 自动数据库事务管理
- 测试数据自动清理
- Redis缓存清理
- 统一的测试客户ID和用户ID

**使用方法**
```php
// 继承基类
class YourTest extends AiSdrTestCase
{
    // 自动获得测试环境设置
    // self::TEST_CLIENT_ID 和 self::TEST_USER_ID 可直接使用
}
```

### 2. AiSdrTestDataFactory 数据工厂

**创建测试数据**
```php
// 创建任务数据
$taskData = AiSdrTestDataFactory::createTaskData([
    'client_id' => self::TEST_CLIENT_ID,
    'source' => Constant::TASK_SOURCE_AI_SDR
]);

// 创建详情数据
$detailData = AiSdrTestDataFactory::createTaskDetailData([
    'task_id' => $taskId,
    'lead_id' => 12345
]);

// 创建AI服务响应数据
$aiResponse = AiSdrTestDataFactory::createQualityAnalysisResultData();
```

### 3. MockServices 服务集合

**创建Mock服务**
```php
// 创建所有Mock服务
$mockServices = MockServices::createAllMockServices();

// 设置Mock响应
$mockServices['recommendApi']->setMatchResults($testData);
$mockServices['leadAutoArchive']->setArchiveResults($leadData);

// 验证服务调用
$this->assertTrue($mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'));
```

## 📊 测试执行和报告

### 自动化测试脚本

**使用测试运行脚本**
```bash
# 运行所有测试
./protected/library/ai_sdr/run_ai_sdr_tests.sh

# 运行特定类型测试
./protected/library/ai_sdr/run_ai_sdr_tests.sh unit
./protected/library/ai_sdr/run_ai_sdr_tests.sh functional
./protected/library/ai_sdr/run_ai_sdr_tests.sh quick
```

**使用问题修复脚本**
```bash
# 运行诊断和修复
./protected/library/ai_sdr/fix_test_issues.sh
```

### 测试报告解读

**单元测试报告示例**
```
Tests: 62, Assertions: 488, Skipped: 8
Time: 00:04.775, Memory: 70.03 MB
```

**关键指标**
- **通过率**: (总测试数 - 跳过数 - 失败数) / 总测试数
- **执行时间**: 应控制在5分钟以内
- **内存使用**: 正常范围70-100MB
- **跳过测试**: 需要分析跳过原因并逐步修复

## 🚨 常见问题和解决方案

### 1. 数据库连接问题

**问题**: `client id 999999 not exist`
**解决**: 使用真实的测试客户ID
```php
// 修改测试基类中的客户ID
protected const TEST_CLIENT_ID = 2642; // 使用真实客户ID
```

### 2. Redis连接错误

**问题**: `ERR syntax error`
**解决**: 检查Redis配置和连接参数
```php
// 在测试中跳过Redis相关测试
if (!$this->isRedisAvailable()) {
    $this->markTestSkipped('Redis not available');
}
```

### 3. 数组字段处理

**问题**: tags字段数据类型错误
**解决**: 确保数组字段正确序列化
```php
$task->tags = [1, 2, 3]; // 确保是数组类型
```

### 4. Mock服务配置

**问题**: Mock服务响应不正确
**解决**: 正确设置Mock响应数据
```php
$mockServices['recommendApi']->setMatchResults([
    'data' => [...], // 确保数据结构正确
    'total' => 1
]);
```

## 📈 测试质量标准

### 代码覆盖率要求
- **核心服务类**: ≥ 90%
- **数据模型类**: ≥ 85%
- **工具和常量类**: ≥ 95%
- **整体覆盖率**: ≥ 85%

### 测试通过率要求
- **单元测试**: ≥ 95%
- **功能测试**: ≥ 90%
- **集成测试**: ≥ 85%
- **整体通过率**: ≥ 90%

### 性能要求
- **单元测试执行时间**: < 10秒
- **功能测试执行时间**: < 30秒
- **集成测试执行时间**: < 60秒
- **内存使用**: < 100MB

## 🔄 持续改进

### 测试维护
1. **定期更新测试数据**: 保持测试数据的时效性
2. **优化测试性能**: 减少不必要的数据库操作
3. **扩展测试覆盖**: 添加新功能的测试用例
4. **修复跳过的测试**: 逐步解决环境和配置问题

### 最佳实践
1. **测试先行**: 新功能开发前先编写测试
2. **小步快跑**: 频繁运行测试，及时发现问题
3. **隔离测试**: 确保测试之间相互独立
4. **文档同步**: 及时更新测试文档和指南

## 🎯 实际测试场景示例

### 场景1: 完整AI SDR工作流测试

**测试目标**: 验证从任务创建到最终交付的完整业务流程

**测试步骤**:
```bash
# 1. 准备测试环境
cd /Users/<USER>/dev/xiaoman/crm/protected/tests

# 2. 创建测试数据
php ../library/ai_sdr/create_test_data.php

# 3. 运行端到端测试
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter "AiSdrCompleteWorkflowTest" \
  integration/ai_sdr/ \
  --verbose

# 4. 验证结果
echo "检查测试输出中的工作流各阶段执行情况"
```

**预期结果**:
- 任务创建成功
- 各阶段状态转换正确
- Mock服务调用验证通过
- 数据持久化正确

### 场景2: 数据库结构验证测试

**测试目标**: 确保所有AI SDR相关表结构完整且字段类型正确

**测试步骤**:
```bash
# 运行数据库结构测试
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter "AiSdrDatabaseStructureTest" \
  integration/ai_sdr/ \
  --verbose
```

**验证要点**:
- 所有必需字段存在
- 字段类型正确 (int, string, array, jsonb)
- 数组字段可正确处理
- 索引配置合理

### 场景3: 外部服务集成测试

**测试目标**: 验证与推荐API、AI服务等外部服务的集成

**测试步骤**:
```bash
# 运行外部服务集成测试
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter "AiServiceIntegrationTest" \
  functional/ai_sdr/ \
  --verbose
```

**Mock服务验证**:
- 推荐API调用正确
- AI服务响应处理正确
- 队列任务分发正常
- 错误处理机制有效

## 🔍 调试和故障排除

### 常用调试命令

**查看详细测试输出**
```bash
# 运行单个测试方法并显示详细信息
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml \
  --filter "testMethodName" \
  --debug \
  --verbose
```

**检查测试数据**
```bash
# 连接数据库查看测试数据
psql -h localhost -U username -d database_name
\dt tbl_ai_sdr*  # 查看AI SDR相关表
```

**查看日志文件**
```bash
# 查看应用日志
tail -f /path/to/application.log

# 查看测试执行日志
tail -f protected/library/ai_sdr/test_report_*.txt
```

### 故障排除检查清单

**环境检查**
- [ ] PHPUnit路径正确
- [ ] 数据库连接正常
- [ ] Redis服务运行
- [ ] 测试客户ID存在
- [ ] 必要的表结构存在

**代码检查**
- [ ] 命名空间正确
- [ ] 类文件路径正确
- [ ] 依赖注入配置正确
- [ ] Mock服务设置正确

**数据检查**
- [ ] 测试数据完整
- [ ] 数组字段格式正确
- [ ] 外键关系正确
- [ ] 时间戳字段有效

## 📚 参考资源

### 相关文档
- [AI SDR README](../library/ai_sdr/README.md) - 功能概述和架构
- [测试最终报告](../library/ai_sdr/TESTING_FINAL_REPORT.md) - 详细测试结果
- [测试执行报告](../library/ai_sdr/TESTING_EXECUTION_REPORT.md) - 当前状况分析

### 工具脚本
- `run_ai_sdr_tests.sh` - 自动化测试运行
- `fix_test_issues.sh` - 问题诊断和修复
- `create_test_data.php` - 测试数据创建

### 配置文件
- `phpunit.xml` - PHPUnit配置
- `test_config.json` - 测试环境配置
- `workflow.yaml` - 业务流程配置

## 🤝 贡献指南

### 添加新测试

1. **确定测试类型**: 单元测试、功能测试或集成测试
2. **选择合适的基类**: AiSdrTestCase 或 WebFunctionalTestCase
3. **遵循命名约定**: `testMethodName_WithCondition_ExpectedBehavior`
4. **使用Mock服务**: 隔离外部依赖
5. **添加适当的断言**: 验证预期行为
6. **更新测试文档**: 记录新增的测试用例

### 测试代码审查要点

- **可读性**: 测试意图清晰，代码易懂
- **独立性**: 测试之间相互独立，无依赖关系
- **完整性**: 覆盖正常流程、边界条件和异常情况
- **性能**: 执行时间合理，资源使用适当
- **维护性**: 易于修改和扩展

---

**维护信息**
- 创建时间: 2024-01-16
- 最后更新: 2024-01-16
- 维护人员: Augment Agent
- 版本: v1.0
- 文档状态: 完整版
