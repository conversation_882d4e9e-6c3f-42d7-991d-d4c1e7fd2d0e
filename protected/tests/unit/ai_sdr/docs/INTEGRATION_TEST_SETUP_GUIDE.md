# AI SDR集成测试环境配置指南

## 概述

本指南说明如何配置AI SDR的集成测试环境，包括数据库集成测试和外部服务集成测试。

## 测试分类

### 1. Mock测试（已完成）✅
- **单元测试**: 62个测试，488个断言
- **功能测试**: 18个测试，132个断言  
- **依赖注入测试**: 19个测试，82个断言
- **端到端集成测试**: 5个测试，79个断言
- **状态**: 100%通过，无需额外配置

### 2. 数据库集成测试（需要配置）⚠️
- **测试文件**: `AiSdrDatabaseIntegrationTest.php`
- **测试数量**: 6个测试
- **状态**: 已创建，需要数据库环境

### 3. 外部服务集成测试（需要配置）⚠️
- **测试文件**: `AiSdrExternalServiceIntegrationTest.php`
- **测试数量**: 8个测试
- **状态**: 已创建，需要外部服务环境

## 数据库集成测试配置

### 前置条件
1. **PostgreSQL数据库**: 可访问的测试数据库
2. **数据库连接**: 正确配置的数据库连接参数
3. **测试用户**: 有效的测试用户和客户端ID

### 配置步骤

#### 1. 数据库连接配置
```php
// 在测试配置中确保数据库连接可用
$testClientId = 1; // 测试客户端ID
$db = \PgActiveRecord::getDbByClientId($testClientId);
```

#### 2. 测试数据准备
```sql
-- 创建测试用的基础数据
INSERT INTO ai_sdr_task (client_id, user_id, source, status) 
VALUES (1, 1, 'ai_sdr', 'pending');

INSERT INTO ai_sdr_task_detail (task_id, domain, company_name, status)
VALUES (1, 'test.com', 'Test Company', 'pending');
```

#### 3. 权限配置
- 确保测试用户有创建、读取、更新、删除AI SDR相关表的权限
- 确保测试用户有访问相关业务表的权限

### 运行数据库集成测试
```bash
cd /path/to/crm/protected/tests
php phpunit --filter AiSdrDatabaseIntegrationTest integration/ai_sdr/
```

### 测试覆盖内容
- ✅ 任务创建和查询
- ✅ 任务详情创建
- ✅ 任务状态更新
- ✅ 查询和过滤功能
- ✅ 事务处理
- ✅ 并发安全性

## 外部服务集成测试配置

### 前置条件
1. **推荐API服务**: 可访问的推荐API服务
2. **AI服务**: 可访问的AI Agent服务
3. **用户认证**: 有效的用户登录状态

### 配置步骤

#### 1. 推荐API配置
```php
// 确保推荐API服务可用
$recommendApi = new RecommendApi($clientId, $userId);
$result = $recommendApi->getRecommendList(); // 测试连接
```

#### 2. AI服务配置
```php
// 确保AI服务可用
$aiAgentFactory = new AiAgentFactory($clientId, $userId);
$agent = $aiAgentFactory->createQualityAnalysisAgent(); // 测试连接
```

#### 3. 用户认证配置
```php
// 确保用户已登录
$user = \User::getLoginUser();
if (!$user) {
    // 设置测试用户登录状态
}
```

### 运行外部服务集成测试
```bash
cd /path/to/crm/protected/tests
php phpunit --filter AiSdrExternalServiceIntegrationTest integration/ai_sdr/
```

### 测试覆盖内容
- ✅ 推荐API公司匹配
- ✅ 推荐API邮箱评级
- ✅ 推荐API公司画像
- ✅ AI Agent质量分析
- ✅ AI Agent EDM写作
- ✅ 外部服务错误处理
- ✅ 外部服务性能测试
- ✅ 外部服务数据一致性

## 测试环境类型

### 1. 开发环境
- **用途**: 日常开发和调试
- **配置**: Mock服务 + 本地数据库
- **运行**: `./run_ai_sdr_tests.sh`

### 2. 测试环境
- **用途**: 集成测试和回归测试
- **配置**: Mock服务 + 测试数据库 + 测试外部服务
- **运行**: `./run_ai_sdr_tests.sh integration`

### 3. 预生产环境
- **用途**: 生产前验证
- **配置**: 真实服务 + 生产级数据库
- **运行**: `./run_ai_sdr_tests.sh production`

## 测试数据管理

### 数据隔离策略
```php
// 每个测试使用独立的数据
protected function setUp(): void
{
    parent::setUp();
    $this->createdTaskIds = [];
    $this->createdDetailIds = [];
}

protected function tearDown(): void
{
    $this->cleanupTestData();
    parent::tearDown();
}
```

### 数据清理机制
```php
protected function cleanupTestData(): void
{
    // 清理任务详情
    foreach ($this->createdDetailIds as $detailId) {
        $detail = AiSdrTaskDetail::findByPk($detailId);
        if ($detail) {
            $detail->delete();
        }
    }
    
    // 清理任务
    foreach ($this->createdTaskIds as $taskId) {
        $task = AiSdrTask::findByPk($taskId);
        if ($task) {
            $task->delete();
        }
    }
}
```

## 性能测试配置

### 响应时间基准
- **推荐API调用**: < 5秒
- **AI Agent创建**: < 3秒
- **数据库操作**: < 1秒

### 并发测试
```php
// 模拟并发创建任务
for ($i = 0; $i < 10; $i++) {
    $taskId = $this->aiSdrService->createAiSdrTask(Constant::TASK_SOURCE_AI_SDR);
    $taskIds[] = $taskId;
}
```

## 监控和日志

### 测试日志配置
```php
// 启用详细日志
error_log('Database not available: ' . $e->getMessage());
error_log('AI Service not available: ' . $e->getMessage());
```

### 性能监控
```php
$startTime = microtime(true);
// 执行测试操作
$endTime = microtime(true);
$executionTime = $endTime - $startTime;
$this->assertLessThan(5.0, $executionTime);
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
Error: Call to a member function createCommand() on null
```
**解决方案**: 检查数据库配置和连接参数

#### 2. 用户认证失败
```
Error: 用户未登录，无法连接client数据
```
**解决方案**: 设置有效的用户登录状态

#### 3. 外部服务不可用
```
Error: Connection timeout
```
**解决方案**: 检查网络连接和服务状态

### 调试技巧

#### 1. 启用详细日志
```bash
export AI_SDR_DEBUG=true
php phpunit --filter AiSdrIntegrationTest
```

#### 2. 单独运行测试
```bash
php phpunit --filter testRealDatabaseTaskCreation
```

#### 3. 跳过不可用的测试
```php
if (!$this->isDatabaseAvailable()) {
    $this->markTestSkipped('Database not available');
}
```

## 最佳实践

### 1. 测试隔离
- 每个测试独立运行
- 不依赖其他测试的状态
- 完整的数据清理

### 2. 错误处理
- 优雅的失败处理
- 详细的错误信息
- 适当的测试跳过

### 3. 性能考虑
- 合理的超时设置
- 批量操作优化
- 资源使用监控

### 4. 维护性
- 清晰的测试结构
- 详细的注释说明
- 易于扩展的设计

## 总结

AI SDR集成测试环境配置包括：
1. ✅ **Mock测试**: 已完成，100%通过
2. ⚠️ **数据库集成测试**: 已创建，需要数据库环境
3. ⚠️ **外部服务集成测试**: 已创建，需要服务环境

通过合理的环境配置，可以实现完整的AI SDR业务流程测试覆盖。
