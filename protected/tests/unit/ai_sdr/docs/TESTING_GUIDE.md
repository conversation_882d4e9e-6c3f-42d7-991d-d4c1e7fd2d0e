# AI SDR 模块 PHPUnit 测试指南

## 当前测试现状分析

### 现有测试问题

通过分析现有的 `AiSdrReadTest.php`，发现以下问题：

1. **测试方法命名不规范**
   - 缺少明确的测试意图描述
   - 方法名过于简单，不能体现测试场景

2. **测试数据依赖性强**
   - 硬编码客户ID和任务ID
   - 依赖生产环境数据
   - 缺少数据隔离

3. **断言不充分**
   - 大量使用 `$this->assertTrue(true)` 空断言
   - 缺少对业务逻辑的验证
   - 没有验证副作用

4. **Mock使用不当**
   - 缺少对外部依赖的Mock
   - AI服务调用没有隔离
   - 数据库操作没有事务回滚

## 主要测试挑战

### 1. 复杂的依赖关系

AI SDR模块涉及多个外部依赖：
- AI服务 (质量分析、背景调研、营销内容生成)
- 推荐API
- EDM服务
- 队列系统
- Redis缓存
- 数据库事务

### 2. 状态机测试复杂性

- 状态转换条件复杂
- 多个状态之间的组合测试
- 异常状态的处理
- 并发状态变更

### 3. 异步任务测试

- 队列任务的执行
- 异步回调处理
- 超时和重试机制
- 任务依赖关系

### 4. 数据一致性

- 多表关联更新
- 事务边界控制
- 并发访问控制
- 数据完整性验证

## 测试架构设计

### 1. 测试分层策略

```
单元测试 (Unit Tests)
├── 核心业务逻辑测试
├── 状态机转换测试
├── 数据模型测试
└── 工具类测试

集成测试 (Integration Tests)
├── 服务间集成测试
├── 数据库集成测试
├── 外部API集成测试
└── 队列集成测试

功能测试 (Functional Tests)
├── 完整业务流程测试
├── 用户场景测试
└── 端到端测试
```

### 2. Mock策略

#### 需要Mock的组件
- AI服务调用
- 外部API (推荐API、EDM服务)
- 队列系统
- 文件上传服务
- 时间函数

#### Mock实现方式
```php
// AI服务Mock
class MockAiAgent extends SdrLeadQualityAnalysisAgent
{
    public function process(array $input): array
    {
        return [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                'confidence' => 0.85,
                'reason' => ['高质量客户', '匹配度高']
            ]
        ];
    }
}

// 推荐API Mock
class MockRecommendApi extends RecommendApi
{
    public function getCompanyProfileByDomains(array $domains): array
    {
        return [
            'example.com' => [
                'company_name' => 'Test Company',
                'main_products' => ['product1', 'product2'],
                'company_type' => ['制造商']
            ]
        ];
    }
}
```

### 3. 测试数据管理

#### 测试数据工厂
```php
class AiSdrTestDataFactory
{
    public static function createTask(array $attributes = []): AiSdrTask
    {
        $defaults = [
            'client_id' => 999999,
            'user_id' => 888888,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'current_stage' => Constant::AI_SDR_STAGE_DIG,
            'end_stage' => Constant::AI_SDR_STAGE_MARKETING,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            'email' => '<EMAIL>',
            'tags' => ['test'],
            'enable_flag' => 1
        ];
        
        $attributes = array_merge($defaults, $attributes);
        
        $task = new AiSdrTask($attributes['client_id']);
        foreach ($attributes as $key => $value) {
            $task->$key = $value;
        }
        $task->create();
        
        return $task;
    }
    
    public static function createTaskDetail(AiSdrTask $task, array $attributes = []): AiSdrTaskDetail
    {
        $defaults = [
            'task_id' => $task->task_id,
            'lead_id' => rand(1000000, 9999999),
            'user_id' => $task->user_id,
            'source' => $task->source,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
            'enable_flag' => 1
        ];
        
        $attributes = array_merge($defaults, $attributes);
        
        $detail = new AiSdrTaskDetail($task->client_id);
        foreach ($attributes as $key => $value) {
            $detail->$key = $value;
        }
        $detail->create();
        
        return $detail;
    }
}
```

#### 数据库事务管理
```php
trait DatabaseTransactions
{
    protected $transactionStarted = false;
    
    public function runDatabaseTransactions()
    {
        $this->beginTransaction();
    }
    
    public function rollback()
    {
        if ($this->transactionStarted) {
            // 回滚所有数据库连接
            foreach ($this->getDatabaseConnections() as $connection) {
                $connection->rollback();
            }
        }
    }
    
    protected function beginTransaction()
    {
        foreach ($this->getDatabaseConnections() as $connection) {
            $connection->beginTransaction();
        }
        $this->transactionStarted = true;
    }
    
    protected function getDatabaseConnections(): array
    {
        return [
            \PgActiveRecord::getDbByClientId(999999),
            \Yii::app()->db
        ];
    }
}
```

## 具体测试实现

### 1. 单元测试示例

#### 测试AISdrService核心方法
```php
class AISdrServiceTest extends \PHPUnit\Framework\TestCase
{
    use DatabaseTransactions;
    
    private $clientId = 999999;
    private $userId = 888888;
    private $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AISdrService($this->clientId, $this->userId);
    }
    
    public function testGetTaskListReturnsValidTasks()
    {
        // Arrange
        $task1 = AiSdrTestDataFactory::createTask([
            'client_id' => $this->clientId,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING
        ]);
        
        $task2 = AiSdrTestDataFactory::createTask([
            'client_id' => $this->clientId,
            'source' => Constant::TASK_SOURCE_IMPORT,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PAUSED // 应该被过滤掉
        ]);
        
        // Act
        $tasks = $this->service->getTaskList();
        
        // Assert
        $this->assertIsArray($tasks);
        $this->assertCount(1, $tasks); // 只有一个有效任务
        $this->assertEquals($task1->task_id, $tasks[Constant::TASK_SOURCE_AI_SDR]['task_id']);
        $this->assertArrayNotHasKey(Constant::TASK_SOURCE_IMPORT, $tasks);
    }
    
    public function testCreateLeadWithValidData()
    {
        // Arrange
        $task = AiSdrTestDataFactory::createTask(['client_id' => $this->clientId]);
        $domain = 'example.com';
        $buyerProfile = [
            'company_name' => 'Test Company',
            'main_products' => ['product1'],
            'company_type' => ['制造商']
        ];
        $leadQuality = Constant::LEAD_QUALITY_HIGH;
        $reason = ['高质量客户'];
        
        // Mock LeadAutoArchive
        $leadAutoArchiveMock = $this->createMock(LeadAutoArchive::class);
        $leadMock = $this->createMock(Lead::class);
        $leadMock->lead_id = 123456;
        $leadMock->image_list = [];
        $leadMock->company_hash_id = 'hash123';
        $leadMock->main_customer_email = '<EMAIL>';
        
        $leadAutoArchiveMock->method('archiveByBatchDomain')
            ->willReturn([$domain => $leadMock]);
        
        // Act
        $detailId = $this->service->createLead(
            $task, 
            1, 
            $leadAutoArchiveMock, 
            $domain, 
            $buyerProfile, 
            $leadQuality, 
            $reason, 
            true
        );
        
        // Assert
        $this->assertIsInt($detailId);
        $this->assertGreaterThan(0, $detailId);
        
        // 验证详情记录是否正确创建
        $detail = new AiSdrTaskDetail($this->clientId, $detailId);
        $this->assertEquals($task->task_id, $detail->task_id);
        $this->assertEquals($leadQuality, $detail->lead_quality);
        $this->assertEquals(1, $detail->enable_flag);
    }
}
```

#### 测试状态机执行器
```php
class SdrDetailExecutorTest extends \PHPUnit\Framework\TestCase
{
    use DatabaseTransactions;
    
    private $clientId = 999999;
    private $userId = 888888;
    private $executor;
    private $task;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->task = AiSdrTestDataFactory::createTask(['client_id' => $this->clientId]);
        $this->executor = new SdrDetailExecutor($this->clientId, $this->userId);
        $this->executor->setTask($this->task);
    }
    
    public function testProcessDetailFromAddToLabel()
    {
        // Arrange
        $detail = AiSdrTestDataFactory::createTaskDetail($this->task, [
            'status' => Constant::DETAIL_STATUS_ADD
        ]);
        
        $sdrDetail = new SdrLeadDetail($this->clientId);
        $sdrDetail->initFromSingle($detail->getAttributes());
        
        // Mock AI服务
        $this->mockAiQualityAnalysis();
        
        // Act
        $result = $this->executor->process([$sdrDetail], Constant::DETAIL_STATUS_LABEL);
        
        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        
        // 验证状态是否正确更新
        $updatedDetail = new AiSdrTaskDetail($this->clientId, $detail->id);
        $this->assertEquals(Constant::DETAIL_STATUS_LABEL, $updatedDetail->status);
    }
    
    private function mockAiQualityAnalysis()
    {
        // 使用反射或依赖注入来替换AI服务
        // 这里需要根据实际架构调整
    }
}
```

### 2. 集成测试示例

```php
class AiSdrIntegrationTest extends \FunctionalTestCase
{
    use DatabaseTransactions;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    public function testCompleteTaskWorkflow()
    {
        // Arrange - 创建完整的测试环境
        $clientId = static::$clientId;
        $userId = static::$userId;
        
        $service = new AISdrService($clientId, $userId);
        
        // 创建任务
        $task = AiSdrTestDataFactory::createTask([
            'client_id' => $clientId,
            'user_id' => $userId,
            'end_stage' => Constant::AI_SDR_STAGE_MARKETING
        ]);
        
        // 创建测试详情
        $detail = AiSdrTestDataFactory::createTaskDetail($task);
        
        // Act - 执行完整流程
        $service->processTask($task->task_id);
        
        // Assert - 验证结果
        $updatedTask = new AiSdrTask($clientId, $task->task_id);
        $this->assertGreaterThanOrEqual(
            Constant::AI_SDR_STAGE_DIG, 
            $updatedTask->current_stage
        );
        
        // 验证详情状态变化
        $updatedDetail = new AiSdrTaskDetail($clientId, $detail->id);
        $this->assertGreaterThan(
            Constant::DETAIL_STATUS_ADD, 
            $updatedDetail->status
        );
    }
}
```

## 测试配置和工具

### 1. PHPUnit配置文件

```xml
<!-- phpunit-ai-sdr.xml -->
<phpunit bootstrap="bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="ai_sdr_unit">
            <directory>unit/ai_sdr</directory>
        </testsuite>
        <testsuite name="ai_sdr_integration">
            <directory>functional/ai_sdr</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">../library/ai_sdr</directory>
            <exclude>
                <directory suffix=".php">../library/ai_sdr/config</directory>
                <directory suffix=".php">../library/ai_sdr/jobs</directory>
            </exclude>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-html" target="coverage/ai_sdr"/>
        <log type="coverage-clover" target="coverage/ai_sdr.xml"/>
    </logging>
</phpunit>
```

### 2. 测试基类

```php
abstract class AiSdrTestCase extends \FunctionalTestCase
{
    use DatabaseTransactions;
    
    protected $testClientId = 999999;
    protected $testUserId = 888888;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestEnvironment();
    }
    
    protected function setupTestEnvironment()
    {
        // 设置测试环境
        \Yii::app()->params['env'] = 'test';
        
        // 清理Redis缓存
        $this->clearRedisCache();
        
        // 设置Mock服务
        $this->setupMockServices();
    }
    
    protected function clearRedisCache()
    {
        $redis = \RedisService::cache();
        $keys = $redis->keys("sdr:*:{$this->testClientId}:*");
        if (!empty($keys)) {
            $redis->del($keys);
        }
    }
    
    protected function setupMockServices()
    {
        // 设置AI服务Mock
        // 设置推荐API Mock
        // 设置队列Mock
    }
}
```

## 运行测试

### 命令行运行
```bash
# 运行所有AI SDR测试
/opt/homebrew/bin/php /path/to/vendor/phpunit/phpunit/phpunit \
  --configuration /path/to/crm/protected/tests/phpunit-ai-sdr.xml \
  --testsuite ai_sdr_unit

# 运行特定测试类
/opt/homebrew/bin/php /path/to/vendor/phpunit/phpunit/phpunit \
  --configuration /path/to/crm/protected/tests/phpunit-ai-sdr.xml \
  --filter AISdrServiceTest

# 生成覆盖率报告
/opt/homebrew/bin/php /path/to/vendor/phpunit/phpunit/phpunit \
  --configuration /path/to/crm/protected/tests/phpunit-ai-sdr.xml \
  --coverage-html coverage/ai_sdr
```

## 最佳实践

### 1. 测试命名规范
- 使用描述性的测试方法名
- 遵循 `test{MethodName}_{Scenario}_{ExpectedResult}` 格式
- 例如：`testProcessTask_WithValidData_ShouldUpdateTaskStage`

### 2. 断言策略
- 使用具体的断言方法而不是通用的assertTrue
- 验证业务逻辑而不仅仅是技术实现
- 检查副作用和状态变化

### 3. 测试数据管理
- 使用工厂模式创建测试数据
- 确保测试数据的隔离性
- 使用事务回滚保证测试环境清洁

### 4. Mock使用原则
- Mock外部依赖而不是内部逻辑
- 保持Mock的简单性和可维护性
- 验证Mock的调用次数和参数

### 5. 性能考虑
- 避免在测试中进行真实的网络调用
- 使用内存数据库进行快速测试
- 并行运行独立的测试用例

## 现有代码修改建议

### 1. AISdrService 可测试性改进

#### 问题：硬编码依赖
```php
// 当前代码 - 难以测试
public function createLead(AiSdrTask $sdrTask, int $productUsageId, LeadAutoArchive $leadAutoArchive, ...)
{
    $leads = $leadAutoArchive->archiveByBatchDomain([$domain], true); // 硬编码调用
    // ...
}
```

#### 建议：依赖注入
```php
// 改进后 - 易于测试
class AISdrService
{
    private $leadAutoArchiveFactory;
    private $aiBackgroundService;
    private $recommendApi;

    public function __construct(
        int $clientId,
        int $userId,
        ?LeadAutoArchiveFactory $leadAutoArchiveFactory = null,
        ?AiBackgroundService $aiBackgroundService = null,
        ?RecommendApi $recommendApi = null
    ) {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->leadAutoArchiveFactory = $leadAutoArchiveFactory ?? new LeadAutoArchiveFactory();
        $this->aiBackgroundService = $aiBackgroundService ?? new AiBackgroundService();
        $this->recommendApi = $recommendApi ?? new RecommendApi($clientId, $userId);
    }

    public function createLead(AiSdrTask $sdrTask, int $productUsageId, string $domain, ...)
    {
        $leadAutoArchive = $this->leadAutoArchiveFactory->create($this->clientId, $this->userId);
        $leads = $leadAutoArchive->archiveByBatchDomain([$domain], true);
        // ...
    }
}
```

### 2. SdrDetailExecutor 状态机测试改进

#### 问题：状态机难以隔离测试
```php
// 当前代码
protected function performAction(object $detail, Transition $nextTransition)
{
    switch ($nextAction) {
        case 'label_lead_quality':
            $agent = new SdrLeadQualityAnalysisAgent($this->clientId, $this->userId); // 硬编码
            $answer = $agent->process([...]);
            break;
    }
}
```

#### 建议：策略模式 + 依赖注入
```php
// 改进后
interface ActionHandlerInterface
{
    public function handle(SdrLeadDetail $detail, array $context): array;
}

class LabelQualityActionHandler implements ActionHandlerInterface
{
    private $qualityAgent;

    public function __construct(SdrLeadQualityAnalysisAgent $qualityAgent)
    {
        $this->qualityAgent = $qualityAgent;
    }

    public function handle(SdrLeadDetail $detail, array $context): array
    {
        return $this->qualityAgent->process($context);
    }
}

class SdrDetailExecutor
{
    private $actionHandlers = [];

    public function __construct(int $clientId, int $userId, array $actionHandlers = [])
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->actionHandlers = $actionHandlers ?: $this->getDefaultActionHandlers();
    }

    protected function performAction(object $detail, Transition $nextTransition)
    {
        $actionName = $nextTransition->getName();
        if (isset($this->actionHandlers[$actionName])) {
            return $this->actionHandlers[$actionName]->handle($detail, $this->getContext());
        }
        throw new \RuntimeException("Unknown action: {$actionName}");
    }
}
```

### 3. 时间依赖问题

#### 问题：时间函数难以测试
```php
// 当前代码
$detail->stage_dig_time = xm_function_now(); // 难以控制时间
```

#### 建议：时间服务抽象
```php
interface TimeServiceInterface
{
    public function now(): string;
    public function timestamp(): int;
}

class SystemTimeService implements TimeServiceInterface
{
    public function now(): string
    {
        return xm_function_now();
    }

    public function timestamp(): int
    {
        return time();
    }
}

class MockTimeService implements TimeServiceInterface
{
    private $fixedTime;

    public function __construct(string $fixedTime = '2024-01-01 10:00:00')
    {
        $this->fixedTime = $fixedTime;
    }

    public function now(): string
    {
        return $this->fixedTime;
    }

    public function timestamp(): int
    {
        return strtotime($this->fixedTime);
    }
}

// 在服务中使用
class AISdrService
{
    private $timeService;

    public function __construct(int $clientId, int $userId, ?TimeServiceInterface $timeService = null)
    {
        $this->timeService = $timeService ?? new SystemTimeService();
    }

    public function updateDetail($detail)
    {
        $detail->stage_dig_time = $this->timeService->now();
    }
}
```

### 4. Redis 缓存测试改进

#### 问题：Redis操作难以测试
```php
// 当前代码
$redis = \RedisService::cache();
$key = sprintf(Constant::REDIS_CACHE_TASK_KEY, $this->clientId, $task->task_id);
$result = $redis->set($key, 1, 'EX', 3600, 'NX');
```

#### 建议：缓存服务抽象
```php
interface CacheServiceInterface
{
    public function set(string $key, $value, int $ttl = 3600): bool;
    public function get(string $key);
    public function delete(string $key): bool;
    public function setNx(string $key, $value, int $ttl = 3600): bool;
}

class RedisCacheService implements CacheServiceInterface
{
    private $redis;

    public function __construct()
    {
        $this->redis = \RedisService::cache();
    }

    public function setNx(string $key, $value, int $ttl = 3600): bool
    {
        return $this->redis->set($key, $value, 'EX', $ttl, 'NX');
    }

    // 其他方法实现...
}

class MockCacheService implements CacheServiceInterface
{
    private $data = [];

    public function setNx(string $key, $value, int $ttl = 3600): bool
    {
        if (isset($this->data[$key])) {
            return false;
        }
        $this->data[$key] = $value;
        return true;
    }

    // 其他方法实现...
}
```

### 5. 队列任务测试改进

#### 问题：队列任务难以同步测试
```php
// 当前代码
$job = new AiSdrProcessTaskJob($this->clientId, $this->task_id);
QueueService::dispatch($job); // 异步执行，难以测试
```

#### 建议：队列服务抽象
```php
interface QueueServiceInterface
{
    public function dispatch(JobInterface $job): void;
    public function dispatchSync(JobInterface $job): void;
}

class AsyncQueueService implements QueueServiceInterface
{
    public function dispatch(JobInterface $job): void
    {
        QueueService::dispatch($job);
    }

    public function dispatchSync(JobInterface $job): void
    {
        $job->handle();
    }
}

class SyncQueueService implements QueueServiceInterface
{
    public function dispatch(JobInterface $job): void
    {
        $job->handle(); // 同步执行，便于测试
    }

    public function dispatchSync(JobInterface $job): void
    {
        $job->handle();
    }
}

// 在测试中使用
class AiSdrServiceTest extends TestCase
{
    public function testProcessTaskDispatchesJob()
    {
        $mockQueue = new SyncQueueService();
        $service = new AISdrService($this->clientId, $this->userId, null, null, null, $mockQueue);

        // 测试会同步执行，便于验证结果
        $service->processTask($taskId);

        // 验证任务执行结果
        $this->assertTaskProcessed($taskId);
    }
}
```

## 具体测试用例实现

### 1. 状态机转换测试

```php
class WorkflowTransitionTest extends AiSdrTestCase
{
    public function testValidTransitions()
    {
        $validTransitions = [
            [Constant::DETAIL_STATUS_ADD, 'label_lead_quality', Constant::DETAIL_STATUS_LABEL],
            [Constant::DETAIL_STATUS_ADD, 'start_background_check', Constant::DETAIL_STATUS_BACKGROUND_CHECKING],
            [Constant::DETAIL_STATUS_LABEL, 'validate_contact', Constant::DETAIL_STATUS_VALIDATE_CONTACTS],
            // 更多转换...
        ];

        foreach ($validTransitions as [$fromStatus, $transition, $toStatus]) {
            $this->assertValidTransition($fromStatus, $transition, $toStatus);
        }
    }

    public function testInvalidTransitions()
    {
        $invalidTransitions = [
            [Constant::DETAIL_STATUS_ADD, 'execute_marketing'], // 不能直接从ADD跳到执行营销
            [Constant::DETAIL_STATUS_VALIDATE_CONTACTS, 'label_lead_quality'], // 不能回退
        ];

        foreach ($invalidTransitions as [$fromStatus, $transition]) {
            $this->assertInvalidTransition($fromStatus, $transition);
        }
    }

    private function assertValidTransition(int $fromStatus, string $transitionName, int $expectedToStatus)
    {
        // 创建测试数据
        $task = AiSdrTestDataFactory::createTask(['client_id' => $this->testClientId]);
        $detail = AiSdrTestDataFactory::createTaskDetail($task, ['status' => $fromStatus]);

        // 创建执行器
        $executor = new SdrDetailExecutor($this->testClientId, $this->testUserId);
        $executor->setTask($task);

        // 执行转换
        $sdrDetail = new SdrLeadDetail($this->testClientId);
        $sdrDetail->initFromSingle($detail->getAttributes());

        $result = $executor->process([$sdrDetail], $expectedToStatus);

        // 验证结果
        $this->assertNotEmpty($result);

        // 验证数据库状态
        $updatedDetail = new AiSdrTaskDetail($this->testClientId, $detail->id);
        $this->assertEquals($expectedToStatus, $updatedDetail->status);
    }
}
```

### 2. 并发控制测试

```php
class ConcurrencyControlTest extends AiSdrTestCase
{
    public function testTaskProcessingLock()
    {
        $task = AiSdrTestDataFactory::createTask(['client_id' => $this->testClientId]);

        // 模拟第一个进程获取锁
        $service1 = new AISdrService($this->testClientId, $this->testUserId);
        $redis = \RedisService::cache();
        $key = sprintf(Constant::REDIS_CACHE_TASK_KEY, $this->testClientId, $task->task_id);

        // 手动设置锁
        $redis->set($key, 1, 'EX', 3600, 'NX');

        // 第二个进程应该无法处理同一任务
        $service2 = new AISdrService($this->testClientId, $this->testUserId);

        // 使用反射或其他方式验证锁机制
        $reflection = new \ReflectionClass($service2);
        $method = $reflection->getMethod('processStageJob');
        $method->setAccessible(true);

        // 应该因为锁而提前返回
        $result = $method->invoke($service2, $task->task_id, $task->current_stage, $task->end_stage);

        // 验证任务状态没有被第二个进程修改
        $unchangedTask = new AiSdrTask($this->testClientId, $task->task_id);
        $this->assertEquals($task->current_stage, $unchangedTask->current_stage);
    }
}
```

### 3. 数据一致性测试

```php
class DataConsistencyTest extends AiSdrTestCase
{
    public function testTaskDetailStatisticsConsistency()
    {
        $task = AiSdrTestDataFactory::createTask(['client_id' => $this->testClientId]);

        // 创建多个详情记录
        $details = [];
        for ($i = 0; $i < 5; $i++) {
            $details[] = AiSdrTestDataFactory::createTaskDetail($task, [
                'enable_flag' => 1,
                'status' => Constant::DETAIL_STATUS_ADD
            ]);
        }

        // 执行统计更新
        $service = new AISdrService($this->testClientId, $this->testUserId);
        $service->updateStatTotal($this->testClientId, $task->task_id, 5);

        // 验证任务统计数据
        $updatedTask = new AiSdrTask($this->testClientId, $task->task_id);
        $this->assertEquals(5, $updatedTask->stat_total);

        // 验证详情记录数量一致性
        $detailFilter = new AiSdrTaskDetailFilter($this->testClientId);
        $detailFilter->task_id = $task->task_id;
        $detailFilter->enable_flag = 1;
        $actualCount = $detailFilter->count();

        $this->assertEquals($updatedTask->stat_total, $actualCount);
    }
}
```

## 测试覆盖率目标

### 覆盖率指标
- **行覆盖率**: ≥ 80%
- **分支覆盖率**: ≥ 70%
- **方法覆盖率**: ≥ 90%

### 重点覆盖区域
1. **核心业务逻辑**: AISdrService 的主要方法
2. **状态机转换**: SdrDetailExecutor 的所有转换路径
3. **数据操作**: ORM 模型的 CRUD 操作
4. **异常处理**: 各种错误场景的处理

### 覆盖率报告生成
```bash
# 生成详细覆盖率报告
/opt/homebrew/bin/php /path/to/vendor/phpunit/phpunit/phpunit \
  --configuration /path/to/crm/protected/tests/phpunit-ai-sdr.xml \
  --coverage-html coverage/ai_sdr \
  --coverage-clover coverage/ai_sdr.xml \
  --log-junit coverage/ai_sdr-junit.xml
```

## 持续集成配置

### CI 流水线示例 (GitLab CI)
```yaml
# .gitlab-ci.yml
test:ai_sdr:
  stage: test
  script:
    - composer install --no-dev --optimize-autoloader
    - php vendor/bin/phpunit --configuration protected/tests/phpunit-ai-sdr.xml
  coverage: '/Lines:\s+(\d+\.\d+\%)/'
  artifacts:
    reports:
      junit: protected/tests/coverage/ai_sdr-junit.xml
      coverage_report:
        coverage_format: cobertura
        path: protected/tests/coverage/ai_sdr.xml
    paths:
      - protected/tests/coverage/
    expire_in: 1 week
  only:
    changes:
      - protected/library/ai_sdr/**/*
      - protected/tests/functional/ai_sdr/**/*
      - protected/tests/unit/ai_sdr/**/*
```

## 总结

通过以上改进建议，AI SDR模块的测试将具备：

1. **更好的可测试性** - 通过依赖注入和接口抽象
2. **更高的测试覆盖率** - 覆盖核心业务逻辑和边界情况
3. **更快的测试执行** - 通过Mock和同步执行
4. **更稳定的测试结果** - 通过数据隔离和事务回滚
5. **更好的维护性** - 通过清晰的测试结构和命名规范

这些改进将显著提高AI SDR模块的代码质量和可维护性。
