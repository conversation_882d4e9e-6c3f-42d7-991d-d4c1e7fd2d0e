# AI SDR 完整功能测试计划

## 🎯 测试目标

基于当前测试状况分析，制定完整的AI SDR功能测试计划，覆盖完整的业务流程和数据库集成测试。

## 📊 当前测试状况总结

### ✅ 已完成的测试覆盖
- **单元测试**: 87%通过率 (54/62个测试方法)
- **功能测试**: 64%通过率 (18/28个测试方法)  
- **数据库集成**: 67%通过率 (4/6个测试方法)
- **总体测试文件**: 14个测试文件，107个测试方法

### ⚠️ 需要修复的关键问题
1. **数据库字段问题**: usage_record_id字段在某些查询中缺失
2. **业务数据缺失**: 缺少运行中的SDR任务测试数据
3. **外部服务集成**: AI服务、推荐API集成测试不完整
4. **控制器文件缺失**: AiSdrImprovedController.php文件不存在

## 🔧 完整测试策略

### 阶段1: 数据库和基础设施修复 (优先级: 高)

#### 1.1 数据库结构验证
- [ ] 验证所有AI SDR相关表的字段完整性
- [ ] 检查usage_record_id字段在所有相关表中的存在性
- [ ] 验证数组字段(tags, product_ids, company_types)的数据类型
- [ ] 检查索引和约束的完整性

#### 1.2 测试数据准备
- [ ] 创建完整的测试数据集
- [ ] 建立运行中的SDR任务测试数据
- [ ] 准备各个阶段的任务详情数据
- [ ] 创建客户档案和买家档案测试数据

#### 1.3 环境配置修复
- [ ] 修复Redis连接配置问题
- [ ] 解决数据库连接配置问题
- [ ] 确保所有外部服务Mock正常工作

### 阶段2: 核心业务流程测试 (优先级: 高)

#### 2.1 完整AI SDR工作流测试
```
挖掘阶段 → 可触达阶段 → 营销阶段 → 有效阶段 → 高价值阶段
```

**测试用例**:
- [ ] 端到端工作流执行测试
- [ ] 各阶段状态转换测试
- [ ] 异常情况处理测试
- [ ] 并发处理测试

#### 2.2 任务生命周期测试
- [ ] 任务创建 (createAiSdrTask)
- [ ] 任务处理 (processTask)
- [ ] 任务暂停和恢复
- [ ] 任务完成和归档
- [ ] 任务统计更新

#### 2.3 详情状态机测试
```
添加 → 打标 → 背调中 → 验证联系人 → 创建营销计划 → 执行营销计划
```

**测试用例**:
- [ ] 状态转换逻辑验证
- [ ] 批量状态更新测试
- [ ] 状态回滚测试
- [ ] 状态机异常处理

### 阶段3: 外部服务集成测试 (优先级: 中)

#### 3.1 AI服务集成
- [ ] 质量分析AI服务 (SdrLeadQualityAnalysisAgent)
- [ ] 背景调研AI服务 (AiBackgroundCheckService)
- [ ] 营销内容生成AI服务
- [ ] AI服务失败处理和重试机制

#### 3.2 推荐API集成
- [ ] 公司推荐API (getMatchCompanyByProfile)
- [ ] 公司档案获取 (getCompanyProfileByDomains)
- [ ] 邮箱质量评级 (getMailQualityRating)
- [ ] API限流和错误处理

#### 3.3 队列服务集成
- [ ] 任务队列分发 (AiSdrDigTaskJob)
- [ ] 质量分析队列 (AiSdrLeadQualityAnalyzeJob)
- [ ] 背景调研队列 (AiSdrHutchLeadJob)
- [ ] 队列失败处理和重试

### 阶段4: 性能和并发测试 (优先级: 中)

#### 4.1 并发控制测试
- [ ] Redis锁机制验证
- [ ] 任务处理并发控制
- [ ] 每日限制并发控制
- [ ] 数据库事务并发控制

#### 4.2 性能测试
- [ ] 大量任务查询性能
- [ ] 批量数据处理性能
- [ ] 内存使用优化验证
- [ ] 数据库查询优化验证

### 阶段5: 端到端业务场景测试 (优先级: 高)

#### 5.1 完整业务场景
**场景1: 新客户AI SDR任务**
```
1. 创建客户档案
2. 创建AI SDR任务
3. 执行公司推荐
4. 质量分析和打标
5. 背景调研
6. 联系人验证
7. 营销计划创建
8. 营销执行
9. 效果统计
```

**场景2: 线索盘活任务**
```
1. 导入历史线索
2. 创建盘活任务
3. 买家档案获取
4. 质量重新评估
5. 营销计划调整
6. 执行营销
7. 效果跟踪
```

#### 5.2 异常场景测试
- [ ] 网络异常处理
- [ ] 服务超时处理
- [ ] 数据不一致处理
- [ ] 资源限制处理

## 🚀 测试执行计划

### 第1周: 基础设施修复
- 修复数据库和环境配置问题
- 完善测试数据准备
- 解决当前失败的测试用例

### 第2周: 核心业务流程测试
- 实现完整工作流测试
- 完善状态机测试
- 加强异常处理测试

### 第3周: 集成和性能测试
- 完善外部服务集成测试
- 实现并发控制测试
- 执行性能基准测试

### 第4周: 端到端场景测试
- 实现完整业务场景测试
- 执行压力测试
- 完善测试文档和报告

## 📋 测试检查清单

### 数据库测试
- [ ] 所有表结构验证通过
- [ ] 字段类型和约束正确
- [ ] 索引性能满足要求
- [ ] 数据一致性验证通过

### 业务逻辑测试
- [ ] 所有状态转换正确
- [ ] 业务规则验证通过
- [ ] 异常处理完善
- [ ] 边界条件覆盖

### 集成测试
- [ ] 所有外部服务集成正常
- [ ] API调用成功率 > 95%
- [ ] 队列处理稳定
- [ ] 缓存机制有效

### 性能测试
- [ ] 响应时间 < 2秒
- [ ] 并发处理能力 > 100
- [ ] 内存使用 < 512MB
- [ ] 数据库连接池稳定

## 🎯 成功标准

- **测试通过率**: ≥ 95%
- **代码覆盖率**: ≥ 90%
- **性能指标**: 满足生产环境要求
- **文档完整性**: 100%覆盖所有功能
- **自动化程度**: ≥ 90%的测试可自动执行

## 📝 下一步行动

1. **立即执行**: 修复数据库字段和环境配置问题
2. **本周完成**: 实现完整的端到端业务流程测试
3. **持续改进**: 建立持续集成和自动化测试流程
