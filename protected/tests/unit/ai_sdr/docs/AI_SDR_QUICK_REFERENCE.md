# AI SDR 测试快速参考

## 🚀 快速命令

### 基础测试执行
```bash
# 进入测试目录
cd /Users/<USER>/dev/xiaoman/crm/protected/tests

# 运行所有AI SDR单元测试
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml --testsuite ai_sdr_unit

# 运行功能测试
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml --testsuite ai_sdr_functional

# 运行特定测试类
/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit \
  --configuration phpunit.xml --filter AISdrServiceTest unit/ai_sdr/
```

### 自动化脚本
```bash
# 运行所有测试
./protected/library/ai_sdr/run_ai_sdr_tests.sh

# 运行单元测试
./protected/library/ai_sdr/run_ai_sdr_tests.sh unit

# 运行功能测试
./protected/library/ai_sdr/run_ai_sdr_tests.sh functional

# 快速测试（核心功能）
./protected/library/ai_sdr/run_ai_sdr_tests.sh quick

# 问题诊断和修复
./protected/library/ai_sdr/fix_test_issues.sh
```

## 📊 测试状态概览

### 当前测试通过率
| 测试类型 | 通过率 | 执行时间 | 状态 |
|----------|--------|----------|------|
| 单元测试 | 87% (54/62) | 4.8秒 | ✅ 良好 |
| 功能测试 | 100% (12/12) | 4.0秒 | ✅ 优秀 |
| 数据库测试 | 67% (4/6) | 10.1秒 | ⚠️ 需优化 |

### 跳过的测试原因
- 数据库连接配置 (3个测试)
- Redis连接语法错误 (2个测试)
- 方法实现待完善 (3个测试)

## 🔧 常见问题快速修复

### 1. 数据库连接问题
```bash
# 问题: client id 999999 not exist
# 解决: 使用真实测试客户ID
# 编辑: protected/tests/unit/ai_sdr/AiSdrTestCase.php
# 修改: protected const TEST_CLIENT_ID = 2642;
```

### 2. Redis连接错误
```bash
# 问题: ERR syntax error
# 解决: 检查Redis配置
redis-cli ping  # 验证Redis服务
```

### 3. 创建测试数据
```bash
# 运行测试数据创建脚本
cd /Users/<USER>/dev/xiaoman/crm
php protected/library/ai_sdr/create_test_data.php
```

### 4. 清理测试环境
```bash
# 清理Redis缓存
redis-cli FLUSHDB

# 重置测试数据
# 测试会自动清理，使用数据库事务回滚
```

## 📝 测试编写模板

### 单元测试模板
```php
<?php
namespace tests\unit\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;

class YourServiceTest extends AiSdrTestCase
{
    protected $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new YourService(self::TEST_CLIENT_ID, self::TEST_USER_ID);
    }
    
    public function testMethodName_WithValidInput_ReturnsExpectedResult()
    {
        // Arrange
        $input = 'test_data';
        
        // Act
        $result = $this->service->methodName($input);
        
        // Assert
        $this->assertEquals('expected', $result);
    }
}
```

### 功能测试模板
```php
<?php
namespace tests\functional\ai_sdr;

use WebFunctionalTestCase;

class YourFunctionalTest extends WebFunctionalTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    public function testApiEndpoint_WithValidRequest_ReturnsSuccess()
    {
        $response = $this->makeApiRequest('/endpoint', ['data' => 'value']);
        $this->assertEquals(200, $response['status']);
    }
}
```

## 🎯 测试重点检查

### 核心业务流程
- [ ] 任务创建 (createAiSdrTask)
- [ ] 任务处理 (processTask)
- [ ] 状态转换 (dig → reachable → marketing → effective)
- [ ] 详情管理 (AiSdrTaskDetail CRUD)
- [ ] 记录追踪 (AiSdrTaskRecord)

### 外部服务集成
- [ ] 推荐API调用 (RecommendApi)
- [ ] AI服务集成 (SdrLeadQualityAnalysisAgent)
- [ ] 队列服务 (QueueService)
- [ ] 线索归档 (LeadAutoArchive)

### 数据一致性
- [ ] 数组字段处理 (tags, product_ids, company_types)
- [ ] JSON字段处理 (data字段)
- [ ] 时间戳字段 (stage_*_time)
- [ ] 外键关系 (task_id, lead_id, detail_id)

## 📈 性能基准

### 执行时间标准
- 单个单元测试: < 0.1秒
- 单个功能测试: < 0.5秒
- 单个集成测试: < 2秒
- 完整测试套件: < 30秒

### 内存使用标准
- 单元测试: < 50MB
- 功能测试: < 80MB
- 集成测试: < 100MB

## 🔍 调试技巧

### 详细输出
```bash
# 显示详细测试信息
--verbose

# 显示调试信息
--debug

# 显示测试覆盖率
--coverage-text
```

### 过滤测试
```bash
# 运行特定方法
--filter testMethodName

# 运行特定类
--filter ClassName

# 运行匹配模式的测试
--filter "pattern"
```

### 测试数据检查
```bash
# 查看测试数据库
psql -h localhost -U user -d test_db

# 查看AI SDR表
\dt tbl_ai_sdr*

# 查看测试任务
SELECT * FROM tbl_ai_sdr_task WHERE client_id = 2642;
```

## 📚 相关文件

### 核心测试文件
- `unit/ai_sdr/AISdrServiceTest.php` - 核心服务测试
- `functional/ai_sdr/AiSdrBasicFunctionalTest.php` - 基础功能测试
- `integration/ai_sdr/AiSdrCompleteWorkflowTest.php` - 端到端测试

### 工具和配置
- `phpunit.xml` - PHPUnit配置
- `protected/library/ai_sdr/run_ai_sdr_tests.sh` - 测试运行脚本
- `protected/library/ai_sdr/fix_test_issues.sh` - 问题修复脚本
- `protected/library/ai_sdr/create_test_data.php` - 测试数据创建

### 文档
- `AI_SDR_TESTING_GUIDE.md` - 完整测试指南
- `protected/library/ai_sdr/TESTING_EXECUTION_REPORT.md` - 执行报告
- `protected/library/ai_sdr/COMPREHENSIVE_TEST_PLAN.md` - 测试计划

## 🚨 紧急故障处理

### 测试完全失败
1. 检查PHPUnit路径和权限
2. 验证数据库连接
3. 确认测试客户ID存在
4. 运行 `fix_test_issues.sh` 脚本

### 数据库相关错误
1. 检查表结构是否存在
2. 验证字段类型和约束
3. 确认测试数据完整性
4. 重新创建测试数据

### Mock服务问题
1. 检查Mock服务配置
2. 验证响应数据格式
3. 确认方法调用追踪
4. 重新初始化Mock服务

---

**快速参考版本**: v1.0  
**最后更新**: 2024-01-16  
**适用范围**: AI SDR模块测试
