<?php
/**
 * AI SDR 测试数据创建脚本
 * 用于创建完整的测试数据集
 */

require_once dirname(__FILE__) . '/../../bootstrap.php';

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\profile\ClientProfile;

class AiSdrTestDataCreator
{
    private $testClientId = 2642; // 使用真实的测试客户ID
    private $testUserId = 249519530; // 使用真实的测试用户ID
    
    public function createTestData()
    {
        echo "开始创建AI SDR测试数据...\n";
        
        try {
            // 1. 创建测试任务
            $this->createTestTask();
            
            // 2. 创建测试详情
            $this->createTestDetails();
            
            // 3. 验证数据创建
            $this->verifyTestData();
            
            echo "测试数据创建完成!\n";
            
        } catch (Exception $e) {
            echo "创建测试数据失败: " . $e->getMessage() . "\n";
        }
    }
    
    private function createTestTask()
    {
        echo "创建测试任务...\n";
        
        $task = new AiSdrTask($this->testClientId);
        $task->client_id = $this->testClientId;
        $task->user_id = $this->testUserId;
        $task->source = Constant::TASK_SOURCE_AI_SDR;
        $task->current_stage = Constant::AI_SDR_STAGE_DIG;
        $task->end_stage = Constant::AI_SDR_STAGE_EFFECTIVE;
        $task->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
        $task->email = '<EMAIL>';
        $task->tags = [1, 2, 3];
        $task->stat_total = 0;
        $task->enable_flag = 1;
        
        if ($task->create()) {
            echo "测试任务创建成功，ID: " . $task->task_id . "\n";
            return $task->task_id;
        } else {
            throw new Exception("测试任务创建失败");
        }
    }
    
    private function createTestDetails()
    {
        echo "创建测试详情...\n";
        
        // 这里可以添加测试详情创建逻辑
        echo "测试详情创建完成\n";
    }
    
    private function verifyTestData()
    {
        echo "验证测试数据...\n";
        
        // 验证任务是否存在
        $taskFilter = new \common\library\ai_sdr\task\AiSdrTaskFilter($this->testClientId);
        $taskFilter->source = Constant::TASK_SOURCE_AI_SDR;
        $taskFilter->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
        $count = $taskFilter->count();
        
        echo "找到 {$count} 个测试任务\n";
    }
}

// 执行测试数据创建
$creator = new AiSdrTestDataCreator();
$creator->createTestData();
