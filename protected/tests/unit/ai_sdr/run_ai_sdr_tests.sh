#!/bin/bash

# AI SDR 测试运行脚本
# 使用方法: ./run_ai_sdr_tests.sh [test_type]

set -e

# 配置变量
TESTS_DIR="/Users/<USER>/dev/xiaoman/crm/protected/tests"
PHPUNIT_BIN="/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit"

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${1}${2}${NC}"
}

# 运行测试
case "${1:-all}" in
    "unit")
        print_message $BLUE "运行AI SDR单元测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --testsuite ai_sdr_unit
        ;;
    "functional")
        print_message $BLUE "运行AI SDR功能测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --filter "RealDatabaseOperationTest|AiSdrBasicFunctionalTest|AiSdrImprovedTest" functional/ai_sdr/
        ;;
    "service")
        print_message $BLUE "运行AI SDR服务测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --filter AISdrServiceTest unit/ai_sdr/AISdrServiceTest.php
        ;;
    "model")
        print_message $BLUE "运行AI SDR模型测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --filter DataModelTest unit/ai_sdr/DataModelTest.php
        ;;
    "constant")
        print_message $BLUE "运行AI SDR常量测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --filter ConstantAndHelperTest unit/ai_sdr/ConstantAndHelperTest.php
        ;;
    "quick")
        print_message $BLUE "运行AI SDR快速测试（单元测试+核心功能测试）..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --testsuite ai_sdr_unit
        print_message $BLUE "运行核心功能测试..."
        $PHPUNIT_BIN --configuration phpunit.xml --filter "RealDatabaseOperationTest|AiSdrBasicFunctionalTest" functional/ai_sdr/
        ;;
    *)
        print_message $BLUE "运行所有AI SDR测试..."
        cd "$TESTS_DIR"
        $PHPUNIT_BIN --configuration phpunit.xml --testsuite ai_sdr_unit
        ;;
esac

print_message $GREEN "测试完成!"