<?php

namespace tests\integration\ai_sdr;

use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use common\library\ai_sdr\dig_record\AiSdrDigRecord;
use common\library\ai_sdr\usage_record\AiProductUsageRecord;
use tests\unit\ai_sdr\AiSdrTestCase;

/**
 * AI SDR 数据库结构验证测试
 * 
 * 验证所有AI SDR相关表的结构完整性和字段正确性
 */
class AiSdrDatabaseStructureTest extends AiSdrTestCase
{
    /**
     * 测试AI SDR任务表结构
     */
    public function testAiSdrTaskTableStructure_HasAllRequiredFields_ReturnsTrue()
    {
        try {
            $task = new AiSdrTask(self::TEST_CLIENT_ID);
            $metadata = $task->getMetadata();
            $columns = $metadata->getColumns();

            // 验证必需字段存在
            $requiredFields = [
                'task_id', 'client_id', 'user_id', 'source', 'current_stage',
                'end_stage', 'task_status', 'email', 'tags', 'stat_total',
                'create_time', 'update_time', 'enable_flag'
            ];

            foreach ($requiredFields as $field) {
                $this->assertArrayHasKey($field, $columns,
                    "Task table should have field: {$field}");
            }

            // 验证字段类型
            $this->assertEquals('int', $columns['task_id']['type']);
            $this->assertEquals('int', $columns['client_id']['type']);
            $this->assertEquals('string', $columns['email']['type']);

            // 验证数组字段
            $this->assertArrayHasKey('tags', $columns, 'Tags field should exist');
            $this->assertEquals('array', $columns['tags']['type'], 'Tags should be array type');
            
            $this->assertTrue(true, 'AI SDR task table structure is valid');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection issue: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AI SDR任务详情表结构
     */
    public function testAiSdrTaskDetailTableStructure_HasAllRequiredFields_ReturnsTrue()
    {
        try {
            $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID);
            $metadata = $detail->getMetadata();
            $columns = $metadata->getColumns();

            // 验证必需字段存在
            $requiredFields = [
                'id', 'client_id', 'user_id', 'task_id', 'lead_id', 'source',
                'stage', 'status', 'usage_record_id', 'lead_quality', 'enable_flag',
                'product_ids', 'company_types', 'public_homepage',
                'stage_dig_time', 'stage_reachable_time', 'stage_marketing_time',
                'stage_effective_time', 'stage_highvalue_time',
                'create_time', 'update_time'
            ];

            foreach ($requiredFields as $field) {
                $this->assertArrayHasKey($field, $columns,
                    "Task detail table should have field: {$field}");
            }

            // 验证关键字段类型
            $this->assertEquals('int', $columns['id']['type']);
            $this->assertEquals('int', $columns['task_id']['type']);
            $this->assertEquals('int', $columns['lead_id']['type']);
            $this->assertEquals('int', $columns['usage_record_id']['type']);

            // 验证数组字段
            $this->assertArrayHasKey('product_ids', $columns, 'Product IDs field should exist');
            $this->assertArrayHasKey('company_types', $columns, 'Company types field should exist');
            
            // 验证时间戳字段
            $stageFields = [
                'stage_dig_time', 'stage_reachable_time', 'stage_marketing_time',
                'stage_effective_time', 'stage_highvalue_time'
            ];

            foreach ($stageFields as $field) {
                $this->assertArrayHasKey($field, $columns, "Stage time field {$field} should exist");
                $this->assertEquals('dateTime', $columns[$field]['type'],
                    "Stage time field {$field} should be dateTime type");
            }
            
            $this->assertTrue(true, 'AI SDR task detail table structure is valid');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection issue: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试AI SDR任务记录表结构
     */
    public function testAiSdrTaskRecordTableStructure_HasAllRequiredFields_ReturnsTrue()
    {
        try {
            $record = new AiSdrTaskRecord(self::TEST_CLIENT_ID);
            $metadata = $record->getMetadata();
            $columns = $metadata->getColumns();

            // 验证必需字段存在
            $requiredFields = [
                'record_id', 'task_id', 'detail_id', 'lead_id', 'type', 'data',
                'estimate_time', 'executed_time', 'refer_type', 'refer_id',
                'enable_flag', 'create_time', 'update_time', 'client_id'
            ];

            foreach ($requiredFields as $field) {
                $this->assertArrayHasKey($field, $columns,
                    "Task record table should have field: {$field}");
            }

            // 验证JSON字段
            $this->assertArrayHasKey('data', $columns, 'Data field should exist');
            $this->assertEquals('jsonb', $columns['data']['type'], 'Data field should be jsonb type');

            // 验证时间字段
            $this->assertEquals('dateTime', $columns['estimate_time']['type']);
            $this->assertEquals('dateTime', $columns['executed_time']['type']);
            
            $this->assertTrue(true, 'AI SDR task record table structure is valid');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection issue: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试数据库连接和基本操作
     */
    public function testDatabaseConnection_CanPerformBasicOperations_ReturnsTrue()
    {
        try {
            // 测试数据库连接
            $db = \ProjectActiveRecord::getDbByClientId(self::TEST_CLIENT_ID);
            $this->assertNotNull($db, 'Database connection should not be null');
            
            // 测试基本查询
            $sql = "SELECT 1 as test_value";
            $result = $db->createCommand($sql)->queryScalar();
            $this->assertEquals(1, $result, 'Basic query should work');
            
            // 测试表存在性
            $tables = [
                'tbl_ai_sdr_task',
                'tbl_ai_sdr_task_detail', 
                'tbl_ai_sdr_task_record',
                'tbl_ai_sdr_dig_record',
                'tbl_ai_product_usage_record'
            ];
            
            foreach ($tables as $table) {
                $sql = "SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{$table}'
                )";
                $exists = $db->createCommand($sql)->queryScalar();
                $this->assertTrue((bool)$exists, "Table {$table} should exist");
            }
            
            $this->assertTrue(true, 'Database connection and basic operations work correctly');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试数组字段的数据类型处理
     */
    public function testArrayFieldDataTypes_CanHandleArrayData_ReturnsTrue()
    {
        try {
            // 测试任务表的tags字段
            $task = new AiSdrTask(self::TEST_CLIENT_ID);
            $task->client_id = self::TEST_CLIENT_ID;
            $task->user_id = self::TEST_USER_ID;
            $task->source = 1;
            $task->current_stage = 1;
            $task->end_stage = 5;
            $task->task_status = 1;
            $task->email = '<EMAIL>';
            $task->tags = [1, 2, 3]; // 测试数组字段
            $task->stat_total = 0;
            $task->enable_flag = 1;
            
            $created = $task->create();
            if ($created) {
                self::$testDataIds['tasks'][] = $task->task_id;
                
                // 验证数组字段保存和读取
                $savedTask = new AiSdrTask(self::TEST_CLIENT_ID, $task->task_id);
                $this->assertIsArray($savedTask->tags, 'Tags should be array type');
                $this->assertEquals([1, 2, 3], $savedTask->tags, 'Tags array should match');
            }
            
            // 测试详情表的数组字段
            $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID);
            $detail->client_id = self::TEST_CLIENT_ID;
            $detail->user_id = self::TEST_USER_ID;
            $detail->task_id = $task->task_id ?? 1;
            $detail->lead_id = 12345;
            $detail->source = 1;
            $detail->stage = 1;
            $detail->status = 1;
            $detail->usage_record_id = 1001;
            $detail->lead_quality = 2;
            $detail->enable_flag = 1;
            $detail->product_ids = [101, 102, 103]; // 测试数组字段
            $detail->company_types = ['Technology', 'Software']; // 测试数组字段
            $detail->public_homepage = ['https://example.com']; // 测试数组字段
            
            $detailCreated = $detail->create();
            if ($detailCreated) {
                self::$testDataIds['details'][] = $detail->id;
                
                // 验证数组字段保存和读取
                $savedDetail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detail->id);
                $this->assertIsArray($savedDetail->product_ids, 'Product IDs should be array type');
                $this->assertIsArray($savedDetail->company_types, 'Company types should be array type');
                $this->assertIsArray($savedDetail->public_homepage, 'Public homepage should be array type');
            }
            
            $this->assertTrue(true, 'Array field data types work correctly');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Array field test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试索引和性能
     */
    public function testDatabaseIndexes_AreOptimizedForQueries_ReturnsTrue()
    {
        try {
            $db = \ProjectActiveRecord::getDbByClientId(self::TEST_CLIENT_ID);
            
            // 检查重要索引是否存在
            $indexQueries = [
                "SELECT indexname FROM pg_indexes WHERE tablename = 'tbl_ai_sdr_task' AND indexname LIKE '%client%'",
                "SELECT indexname FROM pg_indexes WHERE tablename = 'tbl_ai_sdr_task_detail' AND indexname LIKE '%task%'",
                "SELECT indexname FROM pg_indexes WHERE tablename = 'tbl_ai_sdr_task_record' AND indexname LIKE '%task%'"
            ];
            
            foreach ($indexQueries as $query) {
                $indexes = $db->createCommand($query)->queryAll();
                $this->assertNotEmpty($indexes, 'Important indexes should exist');
            }
            
            $this->assertTrue(true, 'Database indexes are properly configured');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Index check failed: ' . $e->getMessage());
        }
    }
}
