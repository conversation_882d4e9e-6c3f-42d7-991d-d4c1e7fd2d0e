<?php

namespace tests\integration\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestDataFactory;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\AiAgentFactory;
use common\library\ai_sdr\Constant;
use common\library\recommend_plaza\RecommendApi;
use common\library\lead\LeadAutoArchive;

require_once __DIR__ . '/../../unit/ai_sdr/MockServices.php';
require_once __DIR__ . '/AiSdrIntegrationTestCase.php';

use tests\unit\ai_sdr\MockServiceFactory;

/**
 * AI SDR外部服务集成测试
 *
 * 测试AI SDR与真实外部服务的集成，验证外部API调用和响应处理
 */
class AiSdrExternalServiceIntegrationTest extends AiSdrIntegrationTestCase
{
    private ?AISdrService $aiSdrService = null;
    private ?SdrDetailExecutor $sdrDetailExecutor = null;
    private ?RecommendCompanyService $recommendCompanyService = null;
    private ?AiAgentFactory $aiAgentFactory = null;
    
    protected function setUp(): void
    {
        parent::setUp();

        // 检查外部服务可用性
        if (!$this->externalServicesAvailable) {
            $this->markTestSkipped('External services not available for integration testing');
        }

        // 创建使用真实外部服务的AI SDR服务
        $this->aiSdrService = $this->createIntegrationAiSdrService();
        $this->sdrDetailExecutor = $this->createIntegrationSdrDetailExecutor();
        $this->recommendCompanyService = $this->createIntegrationRecommendCompanyService();

        $this->aiAgentFactory = new AiAgentFactory(
            $this->getIntegrationTestClientId(),
            $this->getIntegrationTestUserId()
        );
    }
    
    private function areExternalServicesAvailable(): bool
    {
        // 检查推荐API服务可用性
        if (!$this->isRecommendApiAvailable()) {
            return false;
        }
        
        // 检查AI服务可用性
        if (!$this->isAiServiceAvailable()) {
            return false;
        }
        
        return true;
    }
    
    private function isRecommendApiAvailable(): bool
    {
        try {
            $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
            
            // 尝试调用一个简单的API方法
            $result = $recommendApi->getRecommendList();
            
            // 如果返回数组且没有抛出异常，认为服务可用
            return is_array($result);
        } catch (\Exception $e) {
            error_log('RecommendApi not available: ' . $e->getMessage());
            return false;
        }
    }
    
    private function isAiServiceAvailable(): bool
    {
        try {
            // 创建临时的AI Agent工厂进行测试
            $tempAiAgentFactory = new AiAgentFactory($this->getTestClientId(), $this->getTestUserId());
            $qualityAgent = $tempAiAgentFactory->createQualityAnalysisAgent();

            // 如果能创建Agent且没有抛出异常，认为服务可用
            return $qualityAgent !== null;
        } catch (\Exception $e) {
            error_log('AI Service not available: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试真实推荐API的公司匹配功能
     */
    public function testRealRecommendApiCompanyMatching()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        try {
            // 调用真实的公司匹配API
            $matchResults = $recommendApi->getMatchCompanyByProfile(
                1, // 行业匹配
                [1, 2], // 测试行业ID
                ['Software', 'Technology'], // 测试产品
                null, // beforePortraitIds
                [], // excludeDomains
                5 // 小批量测试
            );
            
            // 验证API响应结构
            $this->assertIsArray($matchResults);
            
            if (!empty($matchResults)) {
                // 如果有结果，验证数据结构
                if (isset($matchResults['data']) && is_array($matchResults['data'])) {
                    foreach ($matchResults['data'] as $company) {
                        $this->assertIsArray($company);
                        // 验证必要字段存在
                        $this->assertArrayHasKey('domain', $company);
                        $this->assertArrayHasKey('company_name', $company);
                    }
                }
            }
            
            $this->assertTrue(true, 'Real RecommendApi company matching test passed');
            
        } catch (\Exception $e) {
            // 如果API调用失败，记录错误但不让测试失败
            $this->markTestIncomplete('RecommendApi company matching failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试真实推荐API的邮箱质量评级功能
     */
    public function testRealRecommendApiEmailRating()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        try {
            // 使用测试邮箱进行质量评级
            $testEmails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
            
            $emailRatings = $recommendApi->getMailQualityRating($testEmails);
            
            // 验证API响应
            $this->assertIsArray($emailRatings);
            
            if (!empty($emailRatings)) {
                foreach ($testEmails as $email) {
                    if (isset($emailRatings[$email])) {
                        // 验证评级值在有效范围内
                        $rating = $emailRatings[$email];
                        $this->assertIsInt($rating);
                        $this->assertGreaterThanOrEqual(0, $rating);
                        $this->assertLessThanOrEqual(10, $rating);
                    }
                }
            }
            
            $this->assertTrue(true, 'Real RecommendApi email rating test passed');
            
        } catch (\Exception $e) {
            $this->markTestIncomplete('RecommendApi email rating failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试真实推荐API的公司画像获取功能
     */
    public function testRealRecommendApiCompanyProfile()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        try {
            // 使用知名公司域名进行测试
            $testDomains = ['google.com', 'microsoft.com'];
            
            $profiles = $recommendApi->getCompanyProfileByDomains($testDomains);
            
            // 验证API响应
            $this->assertIsArray($profiles);
            
            if (!empty($profiles)) {
                foreach ($testDomains as $domain) {
                    if (isset($profiles[$domain])) {
                        $profile = $profiles[$domain];
                        $this->assertIsArray($profile);
                        
                        // 验证画像数据结构
                        if (isset($profile['company_name'])) {
                            $this->assertIsString($profile['company_name']);
                        }
                        if (isset($profile['industry'])) {
                            $this->assertIsString($profile['industry']);
                        }
                    }
                }
            }
            
            $this->assertTrue(true, 'Real RecommendApi company profile test passed');
            
        } catch (\Exception $e) {
            $this->markTestIncomplete('RecommendApi company profile failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试真实AI Agent的质量分析功能
     */
    public function testRealAiAgentQualityAnalysis()
    {
        try {
            // 创建真实的质量分析Agent
            $qualityAgent = $this->aiAgentFactory->createQualityAnalysisAgent();
            $this->assertNotNull($qualityAgent);
            
            // 验证Agent类型
            $this->assertInstanceOf(\common\library\ai_agent\SdrLeadQualityAnalysisAgent::class, $qualityAgent);
            
            // 如果Agent有可测试的方法，进行简单测试
            if (method_exists($qualityAgent, 'getClientId')) {
                $this->assertEquals($this->getTestClientId(), $qualityAgent->getClientId());
            }
            
            $this->assertTrue(true, 'Real AI Agent quality analysis test passed');
            
        } catch (\Exception $e) {
            $this->markTestIncomplete('AI Agent quality analysis failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试真实AI Agent的EDM写作功能
     */
    public function testRealAiAgentEdmWriting()
    {
        try {
            // 创建真实的EDM写作Agent
            $edmAgent = $this->aiAgentFactory->createEdmWriteAgent();
            $this->assertNotNull($edmAgent);
            
            // 验证Agent类型
            $this->assertInstanceOf(\common\library\ai_agent\SdrEdmWriteAgent::class, $edmAgent);
            
            // 如果Agent有可测试的方法，进行简单测试
            if (method_exists($edmAgent, 'getClientId')) {
                $this->assertEquals($this->getTestClientId(), $edmAgent->getClientId());
            }
            
            $this->assertTrue(true, 'Real AI Agent EDM writing test passed');
            
        } catch (\Exception $e) {
            $this->markTestIncomplete('AI Agent EDM writing failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试真实外部服务的错误处理
     */
    public function testRealExternalServiceErrorHandling()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        try {
            // 使用无效参数测试错误处理
            $invalidResults = $recommendApi->getMatchCompanyByProfile(
                999, // 无效的匹配类型
                [], // 空的行业ID
                [], // 空的产品
                null,
                [],
                0 // 无效的页面大小
            );
            
            // 验证错误处理：应该返回空结果或抛出可处理的异常
            $this->assertIsArray($invalidResults);
            
            $this->assertTrue(true, 'Real external service error handling test passed');
            
        } catch (\Exception $e) {
            // 验证异常是可预期的类型
            $this->assertInstanceOf(\Exception::class, $e);
            $this->assertNotEmpty($e->getMessage());
            
            $this->assertTrue(true, 'Real external service error handling test passed with expected exception');
        }
    }
    
    /**
     * 测试真实外部服务的性能和超时处理
     */
    public function testRealExternalServicePerformance()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        $startTime = microtime(true);
        
        try {
            // 执行一个简单的API调用
            $result = $recommendApi->getRecommendList();
            
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            
            // 验证响应时间在合理范围内（例如30秒内）
            $this->assertLessThan(30.0, $executionTime, 'API call should complete within 30 seconds');
            
            // 验证返回结果
            $this->assertIsArray($result);
            
            $this->assertTrue(true, 'Real external service performance test passed');
            
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            
            // 即使失败，也要检查是否是超时导致的
            if ($executionTime > 30.0) {
                $this->markTestIncomplete('External service timeout after ' . $executionTime . ' seconds');
            } else {
                $this->markTestIncomplete('External service failed: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * 测试真实外部服务的数据一致性
     */
    public function testRealExternalServiceDataConsistency()
    {
        $recommendApi = new RecommendApi($this->getTestClientId(), $this->getTestUserId());
        
        try {
            // 多次调用相同的API，验证数据一致性
            $result1 = $recommendApi->getRecommendList();
            $result2 = $recommendApi->getRecommendList();
            
            // 验证两次调用的结果结构一致
            $this->assertIsArray($result1);
            $this->assertIsArray($result2);
            
            // 如果有数据，验证数据结构一致性
            if (!empty($result1) && !empty($result2)) {
                $this->assertEquals(array_keys($result1), array_keys($result2));
            }
            
            $this->assertTrue(true, 'Real external service data consistency test passed');
            
        } catch (\Exception $e) {
            $this->markTestIncomplete('External service data consistency test failed: ' . $e->getMessage());
        }
    }
    
    protected function tearDown(): void
    {
        // 外部服务集成测试不需要特殊清理
        parent::tearDown();
    }
}
