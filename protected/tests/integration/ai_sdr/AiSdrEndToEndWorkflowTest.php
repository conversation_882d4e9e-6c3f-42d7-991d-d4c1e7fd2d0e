<?php

namespace tests\integration\ai_sdr;

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;
use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use tests\unit\ai_sdr\MockServices;

/**
 * AI SDR 端到端工作流测试
 * 
 * 测试完整的AI SDR业务流程：从任务创建到潜客交付
 * 覆盖所有阶段：挖掘 → 可触达 → 营销 → 有效 → 高价值
 */
class AiSdrEndToEndWorkflowTest extends AiSdrTestCase
{
    protected $aiSdrService;
    protected $mockServices;
    protected $testTaskId;
    protected $testLeadIds = [];
    protected $testDetailIds = [];
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Mock服务
        $this->mockServices = MockServices::createAllMockServices();
        
        // 创建AI SDR服务实例
        $this->aiSdrService = new AISdrService(
            self::TEST_CLIENT_ID,
            self::TEST_USER_ID,
            $this->mockServices['recommendApi'],
            $this->mockServices['leadAutoArchive'],
            $this->mockServices['queueService']
        );
        
        // 设置Mock服务响应
        $this->setupMockResponses();
    }
    
    protected function tearDown(): void
    {
        // 清理测试数据
        $this->cleanupTestData();
        parent::tearDown();
    }
    
    /**
     * 设置Mock服务响应
     */
    protected function setupMockResponses(): void
    {
        // 推荐API响应 - 模拟公司推荐结果
        $this->mockServices['recommendApi']->setMatchResults([
            'data' => [
                [
                    'id' => 'company_001',
                    'domain' => 'tech-startup.com',
                    'company_name' => 'Tech Startup Inc',
                    'industry' => 'Technology',
                    'match_score' => 0.95,
                    'employee_count' => '50-100',
                    'founding_time' => '2020',
                    'business_strength' => 'High',
                    'company_type' => ['Technology', 'Software'],
                    'main_products' => ['AI Software', 'Cloud Platform'],
                    'product_category' => ['B2B Software'],
                    'country_code' => 'US',
                    'public_homepage' => ['https://tech-startup.com']
                ],
                [
                    'id' => 'company_002',
                    'domain' => 'manufacturing-corp.com',
                    'company_name' => 'Manufacturing Corp',
                    'industry' => 'Manufacturing',
                    'match_score' => 0.88,
                    'employee_count' => '200-500',
                    'founding_time' => '2015',
                    'business_strength' => 'Medium',
                    'company_type' => ['Manufacturing'],
                    'main_products' => ['Industrial Equipment'],
                    'product_category' => ['B2B Equipment'],
                    'country_code' => 'US',
                    'public_homepage' => ['https://manufacturing-corp.com']
                ]
            ],
            'total' => 2
        ]);
        
        // 线索归档服务响应
        $this->mockServices['leadAutoArchive']->setArchiveResults([
            'tech-startup.com' => AiSdrTestDataFactory::createLeadData([
                'domain' => 'tech-startup.com',
                'company_name' => 'Tech Startup Inc'
            ]),
            'manufacturing-corp.com' => AiSdrTestDataFactory::createLeadData([
                'domain' => 'manufacturing-corp.com',
                'company_name' => 'Manufacturing Corp'
            ])
        ]);
        
        // AI质量分析响应
        $this->mockServices['aiServices']->setQualityAnalysisResult([
            'record_id' => 'qa_001',
            'answer' => [
                'domain' => 'tech-startup.com',
                'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                'reason' => ['High match score', 'Good company profile', 'Active in target market']
            ]
        ]);
        
        // 背景调研响应
        $this->mockServices['aiServices']->setBackgroundCheckResult([
            'report_id' => 'bg_001',
            'status' => 'completed',
            'report' => [
                'company_name' => 'Tech Startup Inc',
                'homepage' => 'https://tech-startup.com',
                'has_company' => 1,
                'has_contacts' => 1,
                'employees_min' => 50,
                'employees_max' => 100,
                'industry_ids' => [1, 2],
                'main_products' => 'AI Software, Cloud Platform',
                'company_description' => 'Innovative technology startup focusing on AI solutions'
            ]
        ]);
    }
    
    /**
     * 测试完整的AI SDR工作流：从任务创建到潜客交付
     */
    public function testCompleteAiSdrWorkflow_FromCreationToDelivery_ExecutesAllStages()
    {
        // 阶段1: 创建AI SDR任务
        $task = $this->createAiSdrTask();
        $this->assertNotNull($task, 'AI SDR task should be created successfully');
        $this->testTaskId = $task->task_id;
        
        // 阶段2: 执行挖掘阶段 (DIG)
        $this->executeDigStage($task);
        
        // 阶段3: 执行可触达阶段 (REACHABLE)
        $this->executeReachableStage($task);
        
        // 阶段4: 执行营销阶段 (MARKETING)
        $this->executeMarketingStage($task);
        
        // 阶段5: 执行有效阶段 (EFFECTIVE)
        $this->executeEffectiveStage($task);
        
        // 阶段6: 执行高价值阶段 (HIGH_VALUE) - 可选
        $this->executeHighValueStage($task);
        
        // 阶段7: 验证潜客交付
        $this->verifyLeadDelivery($task);
        
        $this->assertTrue(true, 'Complete AI SDR workflow from creation to delivery executed successfully');
    }
    
    /**
     * 创建AI SDR任务
     */
    protected function createAiSdrTask(): AiSdrTask
    {
        $task = $this->createTestTask([
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'current_stage' => Constant::AI_SDR_STAGE_DIG,
            'end_stage' => Constant::AI_SDR_STAGE_EFFECTIVE,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            'email' => '<EMAIL>',
            'tags' => [1, 2, 3]
        ]);
        
        // 验证任务创建成功
        $this->assertEquals(Constant::AI_SDR_STAGE_DIG, $task->current_stage);
        $this->assertEquals(Constant::AI_SDR_TASK_STATUS_PROCESSING, $task->task_status);
        
        return $task;
    }
    
    /**
     * 执行挖掘阶段
     */
    protected function executeDigStage(AiSdrTask $task): void
    {
        // 执行挖掘逻辑
        $this->aiSdrService->dig($task);
        
        // 验证队列任务被分发
        $this->assertGreaterThan(0, $this->mockServices['queueService']->getDispatchedJobsCount(),
            'Dig stage should dispatch queue jobs');
        
        // 模拟质量分析完成，创建详情记录
        $detail1 = $this->createTestTaskDetail($task, [
            'lead_id' => 100001,
            'source' => Constant::DETAIL_SOURCE_DIG,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_HIGH,
            'usage_record_id' => 1001,
            'product_ids' => [1, 2],
            'company_types' => ['Technology'],
            'public_homepage' => ['https://tech-startup.com'],
            'stage_dig_time' => date('Y-m-d H:i:s')
        ]);
        
        $detail2 = $this->createTestTaskDetail($task, [
            'lead_id' => 100002,
            'source' => Constant::DETAIL_SOURCE_DIG,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_MEDIUM,
            'usage_record_id' => 1002,
            'product_ids' => [1],
            'company_types' => ['Manufacturing'],
            'public_homepage' => ['https://manufacturing-corp.com'],
            'stage_dig_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->testDetailIds[] = $detail1->id;
        $this->testDetailIds[] = $detail2->id;
        $this->testLeadIds[] = $detail1->lead_id;
        $this->testLeadIds[] = $detail2->lead_id;
        
        // 验证挖掘阶段完成
        $this->assertTrue($detail1->id > 0, 'First detail should be created in dig stage');
        $this->assertTrue($detail2->id > 0, 'Second detail should be created in dig stage');
        
        // 创建挖掘记录
        $this->createTestTaskRecord($detail1, [
            'type' => Constant::RECORD_TYPE_ADD_LEAD,
            'data' => ['action' => 'dig', 'result' => 'success'],
            'executed_time' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 执行可触达阶段
     */
    protected function executeReachableStage(AiSdrTask $task): void
    {
        // 更新任务到可触达阶段
        $task->current_stage = Constant::AI_SDR_STAGE_REACHABLE;
        $task->update(['current_stage']);
        
        // 执行背景调研
        $executor = new SdrDetailExecutor(self::TEST_CLIENT_ID, self::TEST_USER_ID);
        $this->aiSdrService->backgroundChecking($task, $executor);
        
        // 更新详情到可触达阶段
        foreach ($this->testDetailIds as $detailId) {
            $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
            $detail->stage = Constant::AI_SDR_STAGE_REACHABLE;
            $detail->status = Constant::DETAIL_STATUS_BACKGROUND_CHECKING;
            $detail->stage_reachable_time = date('Y-m-d H:i:s');
            $detail->update(['stage', 'status', 'stage_reachable_time']);
            
            // 创建背景调研记录
            $this->createTestTaskRecord($detail, [
                'type' => Constant::RECORD_TYPE_BACKGROUND_CHECK,
                'data' => ['action' => 'background_check', 'status' => 'completed'],
                'executed_time' => date('Y-m-d H:i:s')
            ]);
        }
        
        // 验证可触达阶段完成
        $detailFilter = new AiSdrTaskDetailFilter(self::TEST_CLIENT_ID);
        $detailFilter->task_id = $this->testTaskId;
        $detailFilter->stage = Constant::AI_SDR_STAGE_REACHABLE;
        $reachableDetails = $detailFilter->rawData();
        
        $this->assertCount(2, $reachableDetails, 'Should have 2 details in reachable stage');
    }
    
    /**
     * 执行营销阶段
     */
    protected function executeMarketingStage(AiSdrTask $task): void
    {
        // 更新任务到营销阶段
        $task->current_stage = Constant::AI_SDR_STAGE_MARKETING;
        $task->update(['current_stage']);
        
        // 执行营销逻辑
        $executor = new SdrDetailExecutor(self::TEST_CLIENT_ID, self::TEST_USER_ID);
        $this->aiSdrService->marketing($task, $executor);
        
        // 更新详情到营销阶段
        foreach ($this->testDetailIds as $detailId) {
            $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
            $detail->stage = Constant::AI_SDR_STAGE_MARKETING;
            $detail->status = Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN;
            $detail->stage_marketing_time = date('Y-m-d H:i:s');
            $detail->update(['stage', 'status', 'stage_marketing_time']);
            
            // 创建营销计划记录
            $this->createTestTaskRecord($detail, [
                'type' => Constant::RECORD_TYPE_CREATE_MARKETING_PLAN,
                'data' => ['action' => 'create_marketing_plan', 'plan_id' => 'plan_' . $detail->id],
                'executed_time' => date('Y-m-d H:i:s')
            ]);
            
            // 创建营销执行记录
            $this->createTestTaskRecord($detail, [
                'type' => Constant::RECORD_TYPE_EXECUTE_MARKETING_PLAN,
                'data' => ['action' => 'execute_marketing', 'status' => 'sent'],
                'executed_time' => date('Y-m-d H:i:s')
            ]);
        }
        
        // 验证营销记录创建
        $recordFilter = new AiSdrTaskRecordFilter(self::TEST_CLIENT_ID);
        $recordFilter->task_id = $this->testTaskId;
        $recordFilter->type = Constant::RECORD_TYPE_CREATE_MARKETING_PLAN;
        $marketingRecords = $recordFilter->rawData();
        
        $this->assertCount(2, $marketingRecords, 'Should have 2 marketing plan records');
    }
    
    /**
     * 执行有效阶段
     */
    protected function executeEffectiveStage(AiSdrTask $task): void
    {
        // 更新任务到有效阶段
        $task->current_stage = Constant::AI_SDR_STAGE_EFFECTIVE;
        $task->update(['current_stage']);
        
        // 更新高质量详情到有效阶段
        foreach ($this->testDetailIds as $detailId) {
            $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailId);
            
            // 只有高质量线索进入有效阶段
            if ($detail->lead_quality == Constant::LEAD_QUALITY_HIGH) {
                $detail->stage = Constant::AI_SDR_STAGE_EFFECTIVE;
                $detail->status = Constant::DETAIL_STATUS_EFFECTIVE;
                $detail->stage_effective_time = date('Y-m-d H:i:s');
                $detail->update(['stage', 'status', 'stage_effective_time']);
                
                // 创建有效线索记录
                $this->createTestTaskRecord($detail, [
                    'type' => Constant::RECORD_TYPE_EFFECTIVE_LEAD,
                    'data' => ['action' => 'mark_effective', 'quality' => 'high'],
                    'executed_time' => date('Y-m-d H:i:s')
                ]);
            }
        }
        
        // 验证有效阶段完成
        $this->assertEquals(Constant::AI_SDR_STAGE_EFFECTIVE, $task->current_stage,
            'Task should reach effective stage');
    }
    
    /**
     * 执行高价值阶段 (可选)
     */
    protected function executeHighValueStage(AiSdrTask $task): void
    {
        // 如果任务配置了高价值阶段
        if ($task->end_stage >= Constant::AI_SDR_STAGE_HIGH_VALUE) {
            $task->current_stage = Constant::AI_SDR_STAGE_HIGH_VALUE;
            $task->update(['current_stage']);
            
            // 筛选高价值线索
            $detailFilter = new AiSdrTaskDetailFilter(self::TEST_CLIENT_ID);
            $detailFilter->task_id = $this->testTaskId;
            $detailFilter->stage = Constant::AI_SDR_STAGE_EFFECTIVE;
            $detailFilter->lead_quality = Constant::LEAD_QUALITY_HIGH;
            $effectiveDetails = $detailFilter->rawData();
            
            foreach ($effectiveDetails as $detailData) {
                $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID, $detailData['id']);
                $detail->stage = Constant::AI_SDR_STAGE_HIGH_VALUE;
                $detail->status = Constant::DETAIL_STATUS_HIGH_VALUE;
                $detail->stage_highvalue_time = date('Y-m-d H:i:s');
                $detail->update(['stage', 'status', 'stage_highvalue_time']);
            }
        }
    }
    
    /**
     * 验证潜客交付
     */
    protected function verifyLeadDelivery(AiSdrTask $task): void
    {
        // 验证任务完成状态
        $this->assertEquals(Constant::AI_SDR_STAGE_EFFECTIVE, $task->current_stage,
            'Task should be in effective stage');
        
        // 验证详情记录完整性
        $detailFilter = new AiSdrTaskDetailFilter(self::TEST_CLIENT_ID);
        $detailFilter->task_id = $this->testTaskId;
        $allDetails = $detailFilter->rawData();
        
        $this->assertGreaterThan(0, count($allDetails), 'Task should have detail records');
        
        // 验证有效线索数量
        $effectiveDetails = array_filter($allDetails, function($detail) {
            return $detail['stage'] == Constant::AI_SDR_STAGE_EFFECTIVE;
        });
        
        $this->assertGreaterThan(0, count($effectiveDetails), 'Should have effective leads');
        
        // 验证记录完整性
        $recordFilter = new AiSdrTaskRecordFilter(self::TEST_CLIENT_ID);
        $recordFilter->task_id = $this->testTaskId;
        $allRecords = $recordFilter->rawData();
        
        $this->assertGreaterThan(0, count($allRecords), 'Task should have execution records');
        
        // 验证各阶段记录存在
        $recordTypes = array_column($allRecords, 'type');
        $this->assertContains(Constant::RECORD_TYPE_ADD_LEAD, $recordTypes, 'Should have add lead records');
        $this->assertContains(Constant::RECORD_TYPE_BACKGROUND_CHECK, $recordTypes, 'Should have background check records');
        $this->assertContains(Constant::RECORD_TYPE_CREATE_MARKETING_PLAN, $recordTypes, 'Should have marketing plan records');
        
        // 验证所有Mock服务被正确调用
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'),
            'Recommend API should be called');
        $this->assertTrue($this->mockServices['leadAutoArchive']->wasMethodCalled('archiveByBatchDomain'),
            'Lead auto archive should be called');
        $this->assertGreaterThan(0, $this->mockServices['queueService']->getDispatchedJobsCount(),
            'Queue service should dispatch jobs');
        
        // 验证潜客交付质量
        foreach ($effectiveDetails as $detail) {
            $this->assertNotEmpty($detail['stage_dig_time'], 'Effective lead should have dig time');
            $this->assertNotEmpty($detail['stage_reachable_time'], 'Effective lead should have reachable time');
            $this->assertNotEmpty($detail['stage_marketing_time'], 'Effective lead should have marketing time');
            $this->assertNotEmpty($detail['stage_effective_time'], 'Effective lead should have effective time');
        }
    }
    
    /**
     * 测试异常情况处理
     */
    public function testWorkflowExceptionHandling_WithServiceFailures_HandlesGracefully()
    {
        // 设置服务失败响应
        $this->mockServices['recommendApi']->setFailureMode(true);
        
        $task = $this->createTestTask();
        
        try {
            $this->aiSdrService->dig($task);
            $this->assertTrue(true, 'Should handle service failures gracefully');
        } catch (\Exception $e) {
            $this->assertStringContains('service', strtolower($e->getMessage()),
                'Exception should indicate service failure');
        }
    }
    
    /**
     * 测试并发处理
     */
    public function testConcurrentTaskProcessing_WithMultipleTasks_HandlesCorrectly()
    {
        // 创建多个任务模拟并发处理
        $task1 = $this->createTestTask(['email' => '<EMAIL>']);
        $task2 = $this->createTestTask(['email' => '<EMAIL>']);
        
        // 验证任务独立处理
        $this->assertNotEquals($task1->task_id, $task2->task_id, 'Tasks should have different IDs');
        
        // 执行并发挖掘
        $this->aiSdrService->dig($task1);
        $this->aiSdrService->dig($task2);
        
        // 验证队列任务正确分发
        $this->assertGreaterThan(0, $this->mockServices['queueService']->getDispatchedJobsCount(),
            'Concurrent tasks should dispatch queue jobs');
    }
}
