<?php

namespace tests\integration\ai_sdr;

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;
use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use tests\unit\ai_sdr\MockServices;

/**
 * AI SDR 完整工作流集成测试
 * 
 * 测试完整的AI SDR业务流程，从任务创建到最终交付
 */
class AiSdrCompleteWorkflowTest extends AiSdrTestCase
{
    protected $aiSdrService;
    protected $mockServices;
    protected $testTaskId;
    protected $testLeadIds = [];
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Mock服务
        $this->mockServices = MockServices::createAllMockServices();
        
        // 创建AI SDR服务实例
        $this->aiSdrService = new AISdrService(
            self::TEST_CLIENT_ID,
            self::TEST_USER_ID,
            $this->mockServices['recommendApi'],
            $this->mockServices['leadAutoArchive'],
            $this->mockServices['queueService']
        );
        
        // 准备测试数据
        $this->prepareTestData();
    }
    
    protected function tearDown(): void
    {
        // 清理测试数据
        $this->cleanupTestData();
        parent::tearDown();
    }
    
    /**
     * 准备测试数据
     */
    protected function prepareTestData(): void
    {
        // 创建测试任务
        $taskData = AiSdrTestDataFactory::createTaskData([
            'client_id' => self::TEST_CLIENT_ID,
            'user_id' => self::TEST_USER_ID,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'current_stage' => Constant::AI_SDR_STAGE_DIG,
            'end_stage' => Constant::AI_SDR_STAGE_EFFECTIVE,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            'email' => '<EMAIL>',
            'tags' => [1, 2, 3]
        ]);
        
        $task = new AiSdrTask(self::TEST_CLIENT_ID);
        foreach ($taskData as $key => $value) {
            $task->$key = $value;
        }
        
        if ($task->create()) {
            $this->testTaskId = $task->task_id;
            self::$testDataIds['tasks'][] = $this->testTaskId;
        }
        
        // 设置Mock服务响应
        $this->setupMockResponses();
    }
    
    /**
     * 设置Mock服务响应
     */
    protected function setupMockResponses(): void
    {
        // 推荐API响应
        $this->mockServices['recommendApi']->setMatchResults([
            'data' => [
                [
                    'id' => 'company_001',
                    'domain' => 'tech-company.com',
                    'company_name' => 'Tech Company Ltd',
                    'industry' => 'Technology',
                    'match_score' => 0.92,
                    'employee_count' => '100-500',
                    'founding_time' => '2010',
                    'business_strength' => 'High',
                    'company_type' => ['Technology', 'Software'],
                    'main_products' => ['Software', 'Cloud Services'],
                    'product_category' => ['B2B Software'],
                    'country_code' => 'US',
                    'public_homepage' => ['https://tech-company.com']
                ]
            ],
            'total' => 1
        ]);
        
        // 线索归档服务响应
        $leadData = AiSdrTestDataFactory::createLeadData();
        $this->mockServices['leadAutoArchive']->setArchiveResults([
            'tech-company.com' => $leadData
        ]);
        
        // AI质量分析响应
        $this->mockServices['aiServices']->setQualityAnalysisResult([
            'record_id' => 'qa_001',
            'answer' => [
                'domain' => 'tech-company.com',
                'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                'reason' => ['High match score', 'Good company profile', 'Active business']
            ]
        ]);
        
        // 背景调研响应
        $this->mockServices['aiServices']->setBackgroundCheckResult([
            'report_id' => 'bg_001',
            'status' => 'completed',
            'report' => [
                'company_name' => 'Tech Company Ltd',
                'homepage' => 'https://tech-company.com',
                'has_company' => 1,
                'has_contacts' => 1,
                'employees_min' => 100,
                'employees_max' => 500,
                'industry_ids' => [1, 2],
                'main_products' => 'Software, Cloud Services',
                'company_description' => 'Leading technology company'
            ]
        ]);
    }
    
    /**
     * 测试完整的AI SDR工作流
     */
    public function testCompleteAiSdrWorkflow_FromDigToEffective_ExecutesAllStages()
    {
        // 验证任务已创建
        $this->assertNotEmpty($this->testTaskId, 'Test task should be created');
        
        $task = new AiSdrTask(self::TEST_CLIENT_ID, $this->testTaskId);
        $this->assertFalse($task->isNew(), 'Task should exist in database');
        
        // 阶段1: 挖掘阶段 (DIG)
        $this->executeDigStage($task);
        
        // 阶段2: 可触达阶段 (REACHABLE) 
        $this->executeReachableStage($task);
        
        // 阶段3: 营销阶段 (MARKETING)
        $this->executeMarketingStage($task);
        
        // 阶段4: 有效阶段 (EFFECTIVE)
        $this->executeEffectiveStage($task);
        
        // 验证最终状态
        $this->verifyFinalState($task);
        
        $this->assertTrue(true, 'Complete AI SDR workflow executed successfully');
    }
    
    /**
     * 执行挖掘阶段
     */
    protected function executeDigStage(AiSdrTask $task): void
    {
        // 模拟挖掘过程
        $this->aiSdrService->dig($task);
        
        // 验证队列任务被分发
        $this->assertGreaterThan(0, $this->mockServices['queueService']->getDispatchedJobsCount(),
            'Dig stage should dispatch queue jobs');
        
        // 模拟质量分析完成，创建详情记录
        $detailData = AiSdrTestDataFactory::createTaskDetailData([
            'task_id' => $this->testTaskId,
            'lead_id' => 12345,
            'user_id' => self::TEST_USER_ID,
            'source' => Constant::DETAIL_SOURCE_DIG,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_HIGH,
            'usage_record_id' => 1001,
            'enable_flag' => 1
        ]);
        
        $detail = new AiSdrTaskDetail(self::TEST_CLIENT_ID);
        foreach ($detailData as $key => $value) {
            $detail->$key = $value;
        }
        
        if ($detail->create()) {
            $this->testLeadIds[] = $detail->lead_id;
            self::$testDataIds['details'][] = $detail->id;
        }
        
        // 验证挖掘阶段完成
        $this->assertTrue($detail->id > 0, 'Detail should be created in dig stage');
    }
    
    /**
     * 执行可触达阶段
     */
    protected function executeReachableStage(AiSdrTask $task): void
    {
        // 更新任务到可触达阶段
        $task->current_stage = Constant::AI_SDR_STAGE_REACHABLE;
        $task->update(['current_stage']);
        
        // 执行背景调研
        $this->aiSdrService->backgroundChecking($task, new SdrDetailExecutor(self::TEST_CLIENT_ID, self::TEST_USER_ID));
        
        // 验证详情状态更新
        $detailFilter = new AiSdrTaskDetailFilter(self::TEST_CLIENT_ID);
        $detailFilter->task_id = $this->testTaskId;
        $detailFilter->stage = Constant::AI_SDR_STAGE_REACHABLE;
        $details = $detailFilter->rawData();
        
        $this->assertNotEmpty($details, 'Details should be updated to reachable stage');
    }
    
    /**
     * 执行营销阶段
     */
    protected function executeMarketingStage(AiSdrTask $task): void
    {
        // 更新任务到营销阶段
        $task->current_stage = Constant::AI_SDR_STAGE_MARKETING;
        $task->update(['current_stage']);
        
        // 执行营销逻辑
        $executor = new SdrDetailExecutor(self::TEST_CLIENT_ID, self::TEST_USER_ID);
        $this->aiSdrService->marketing($task, $executor);
        
        // 验证营销记录创建
        $recordFilter = new AiSdrTaskRecordFilter(self::TEST_CLIENT_ID);
        $recordFilter->task_id = $this->testTaskId;
        $recordFilter->type = Constant::RECORD_TYPE_CREATE_MARKETING_PLAN;
        $records = $recordFilter->rawData();
        
        $this->assertNotEmpty($records, 'Marketing records should be created');
    }
    
    /**
     * 执行有效阶段
     */
    protected function executeEffectiveStage(AiSdrTask $task): void
    {
        // 更新任务到有效阶段
        $task->current_stage = Constant::AI_SDR_STAGE_EFFECTIVE;
        $task->update(['current_stage']);
        
        // 验证任务状态
        $this->assertEquals(Constant::AI_SDR_STAGE_EFFECTIVE, $task->current_stage,
            'Task should reach effective stage');
    }
    
    /**
     * 验证最终状态
     */
    protected function verifyFinalState(AiSdrTask $task): void
    {
        // 验证任务完成状态
        $this->assertEquals(Constant::AI_SDR_STAGE_EFFECTIVE, $task->current_stage,
            'Task should be in effective stage');
        
        // 验证详情记录完整性
        $detailFilter = new AiSdrTaskDetailFilter(self::TEST_CLIENT_ID);
        $detailFilter->task_id = $this->testTaskId;
        $detailCount = $detailFilter->count();
        
        $this->assertGreaterThan(0, $detailCount, 'Task should have detail records');
        
        // 验证记录完整性
        $recordFilter = new AiSdrTaskRecordFilter(self::TEST_CLIENT_ID);
        $recordFilter->task_id = $this->testTaskId;
        $recordCount = $recordFilter->count();
        
        $this->assertGreaterThan(0, $recordCount, 'Task should have execution records');
        
        // 验证所有Mock服务被正确调用
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'),
            'Recommend API should be called');
        $this->assertTrue($this->mockServices['leadAutoArchive']->wasMethodCalled('archiveByBatchDomain'),
            'Lead auto archive should be called');
        $this->assertGreaterThan(0, $this->mockServices['queueService']->getDispatchedJobsCount(),
            'Queue service should dispatch jobs');
    }
    
    /**
     * 测试异常情况处理
     */
    public function testWorkflowExceptionHandling_WithServiceFailures_HandlesGracefully()
    {
        // 设置服务失败响应
        $this->mockServices['recommendApi']->setFailureMode(true);
        
        $task = new AiSdrTask(self::TEST_CLIENT_ID, $this->testTaskId);
        
        try {
            $this->aiSdrService->dig($task);
            $this->assertTrue(true, 'Should handle service failures gracefully');
        } catch (\Exception $e) {
            $this->assertStringContains('service', strtolower($e->getMessage()),
                'Exception should indicate service failure');
        }
    }
}
