<?php

namespace tests\integration\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\AiAgentFactory;
use common\library\recommend_plaza\RecommendApi;
use common\library\lead\LeadAutoArchive;

/**
 * AI SDR集成测试基类
 * 
 * 提供数据库和外部服务集成测试的基础功能
 */
abstract class AiSdrIntegrationTestCase extends AiSdrTestCase
{
    // 使用测试环境的真实配置
    protected const INTEGRATION_TEST_CLIENT_ID = 2; // 从test.php配置中获取
    protected const INTEGRATION_TEST_USER_ID = 765; // 从test.php配置中获取
    
    protected bool $databaseAvailable = false;
    protected bool $externalServicesAvailable = false;
    protected array $createdTestData = [];
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 检查集成测试环境
        $this->checkIntegrationEnvironment();
        
        // 设置测试用户登录状态
        $this->setupTestUserSession();
        
        // 初始化测试数据追踪
        $this->createdTestData = [
            'tasks' => [],
            'details' => [],
            'leads' => []
        ];
    }
    
    protected function tearDown(): void
    {
        // 清理集成测试数据
        $this->cleanupIntegrationTestData();
        
        parent::tearDown();
    }
    
    /**
     * 检查集成测试环境可用性
     */
    protected function checkIntegrationEnvironment(): void
    {
        $this->databaseAvailable = $this->isDatabaseAvailable();
        $this->externalServicesAvailable = $this->areExternalServicesAvailable();
        
        // 记录环境状态
        if (!$this->databaseAvailable) {
            error_log('Integration test: Database not available');
        }
        
        if (!$this->externalServicesAvailable) {
            error_log('Integration test: External services not available');
        }
    }
    
    /**
     * 检查数据库是否可用
     */
    protected function isDatabaseAvailable(): bool
    {
        try {
            // 检查PostgreSQL连接
            $db = \PgActiveRecord::getDbByClientId($this->getIntegrationTestClientId());
            if ($db === null) {
                return false;
            }
            
            $result = $db->createCommand('SELECT 1')->queryScalar();
            return $result == 1;
        } catch (\Exception $e) {
            error_log('Database check failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查外部服务是否可用
     */
    protected function areExternalServicesAvailable(): bool
    {
        return $this->isRecommendApiAvailable() && $this->isAiServiceAvailable();
    }
    
    /**
     * 检查推荐API是否可用
     */
    protected function isRecommendApiAvailable(): bool
    {
        try {
            $recommendApi = new RecommendApi(
                $this->getIntegrationTestClientId(), 
                $this->getIntegrationTestUserId()
            );
            
            // 尝试调用一个简单的API方法
            $result = $recommendApi->getRecommendList();
            return is_array($result);
        } catch (\Exception $e) {
            error_log('RecommendApi check failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查AI服务是否可用
     */
    protected function isAiServiceAvailable(): bool
    {
        try {
            $aiAgentFactory = new AiAgentFactory(
                $this->getIntegrationTestClientId(), 
                $this->getIntegrationTestUserId()
            );
            
            $qualityAgent = $aiAgentFactory->createQualityAnalysisAgent();
            return $qualityAgent !== null;
        } catch (\Exception $e) {
            error_log('AI Service check failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 设置测试用户登录状态
     */
    protected function setupTestUserSession(): void
    {
        try {
            // 跳过用户登录状态设置，避免类型错误
            // 在实际的集成测试环境中，应该有正确的用户登录机制
            // 这里我们依赖测试环境的默认配置

            // 如果需要设置用户状态，应该使用正确的User类实例
            // 而不是stdClass

        } catch (\Exception $e) {
            error_log('Failed to setup test user session: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建集成测试的AI SDR服务
     */
    protected function createIntegrationAiSdrService(): AISdrService
    {
        return new AISdrService(
            $this->getIntegrationTestClientId(),
            $this->getIntegrationTestUserId(),
            null, // 使用真实的RecommendApi
            null, // 使用真实的LeadAutoArchive
            null  // 使用真实的QueueService
        );
    }
    
    /**
     * 创建集成测试的SdrDetailExecutor
     */
    protected function createIntegrationSdrDetailExecutor(): SdrDetailExecutor
    {
        return new SdrDetailExecutor(
            $this->getIntegrationTestClientId(),
            $this->getIntegrationTestUserId(),
            null, // 使用真实的RecommendApi
            null  // 使用真实的AiAgentFactory
        );
    }
    
    /**
     * 创建集成测试的RecommendCompanyService
     */
    protected function createIntegrationRecommendCompanyService(): RecommendCompanyService
    {
        return new RecommendCompanyService(
            $this->getIntegrationTestClientId(),
            $this->getIntegrationTestUserId(),
            null, // 使用真实的RecommendApi
            null  // 使用真实的AiAgentFactory
        );
    }
    
    /**
     * 安全地执行数据库操作
     */
    protected function safeExecuteDatabase(callable $operation, string $operationName = 'Database operation')
    {
        if (!$this->databaseAvailable) {
            $this->markTestSkipped("Database not available for {$operationName}");
        }
        
        try {
            return $operation();
        } catch (\Exception $e) {
            $this->markTestIncomplete("{$operationName} failed: " . $e->getMessage());
        }
    }
    
    /**
     * 安全地执行外部服务操作
     */
    protected function safeExecuteExternalService(callable $operation, string $serviceName = 'External service')
    {
        if (!$this->externalServicesAvailable) {
            $this->markTestSkipped("{$serviceName} not available");
        }
        
        try {
            return $operation();
        } catch (\Exception $e) {
            $this->markTestIncomplete("{$serviceName} operation failed: " . $e->getMessage());
        }
    }
    
    /**
     * 记录创建的测试数据
     */
    protected function trackCreatedData(string $type, $id): void
    {
        if (!isset($this->createdTestData[$type])) {
            $this->createdTestData[$type] = [];
        }
        $this->createdTestData[$type][] = $id;
    }
    
    /**
     * 清理集成测试数据
     */
    protected function cleanupIntegrationTestData(): void
    {
        if (!$this->databaseAvailable) {
            return;
        }
        
        try {
            // 清理任务详情
            foreach ($this->createdTestData['details'] ?? [] as $detailId) {
                $this->safeDeleteTaskDetail($detailId);
            }
            
            // 清理任务
            foreach ($this->createdTestData['tasks'] ?? [] as $taskId) {
                $this->safeDeleteTask($taskId);
            }
            
            // 清理线索（如果需要）
            foreach ($this->createdTestData['leads'] ?? [] as $leadId) {
                $this->safeDeleteLead($leadId);
            }
        } catch (\Exception $e) {
            error_log('Failed to cleanup integration test data: ' . $e->getMessage());
        }
    }
    
    /**
     * 安全删除任务
     */
    protected function safeDeleteTask(int $taskId): void
    {
        try {
            $task = new \common\library\ai_sdr\task\AiSdrTask($this->getIntegrationTestClientId(), $taskId);
            if (!$task->isNew()) {
                $task->delete();
            }
        } catch (\Exception $e) {
            error_log("Failed to delete task {$taskId}: " . $e->getMessage());
        }
    }

    /**
     * 安全删除任务详情
     */
    protected function safeDeleteTaskDetail(int $detailId): void
    {
        try {
            $detail = new \common\library\ai_sdr\task_detail\AiSdrTaskDetail($this->getIntegrationTestClientId(), $detailId);
            if (!$detail->isNew()) {
                $detail->delete();
            }
        } catch (\Exception $e) {
            error_log("Failed to delete task detail {$detailId}: " . $e->getMessage());
        }
    }
    
    /**
     * 安全删除线索
     */
    protected function safeDeleteLead(int $leadId): void
    {
        try {
            $lead = \common\library\lead\Lead::findByPk($leadId);
            if ($lead) {
                $lead->delete();
            }
        } catch (\Exception $e) {
            error_log("Failed to delete lead {$leadId}: " . $e->getMessage());
        }
    }
    
    /**
     * 获取集成测试客户ID
     */
    protected function getIntegrationTestClientId(): int
    {
        return self::INTEGRATION_TEST_CLIENT_ID;
    }
    
    /**
     * 获取集成测试用户ID
     */
    protected function getIntegrationTestUserId(): int
    {
        return self::INTEGRATION_TEST_USER_ID;
    }
    
    /**
     * 断言数据库操作成功
     */
    protected function assertDatabaseOperationSuccess($result, string $operation): void
    {
        $this->assertNotFalse($result, "Database operation '{$operation}' should succeed");
    }
    
    /**
     * 断言外部服务响应有效
     */
    protected function assertExternalServiceResponse($response, string $service): void
    {
        $this->assertNotNull($response, "External service '{$service}' should return valid response");
        $this->assertIsArray($response, "External service '{$service}' should return array response");
    }
    
    /**
     * 获取测试环境配置
     */
    protected function getTestEnvironmentConfig(): array
    {
        return \Yii::app()->params['unit_test'] ?? [];
    }
    
    /**
     * 检查是否在CI环境中运行
     */
    protected function isRunningInCI(): bool
    {
        return !empty(getenv('CI')) || !empty(getenv('GITHUB_ACTIONS'));
    }
}
