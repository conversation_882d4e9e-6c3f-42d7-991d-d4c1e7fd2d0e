<?php
/**
 * AI SDR 测试数据准备脚本
 * 
 * 用于创建完整的AI SDR测试数据，包括运行中的任务、详情记录等
 */

require_once dirname(__FILE__) . '/../bootstrap.php';

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;
use common\library\ai_sdr\profile\ClientProfile;

class AiSdrTestDataPreparer
{
    private $testClientId = 2642; // 使用真实的测试客户ID
    private $testUserId = 249519530; // 使用真实的测试用户ID
    private $createdTaskIds = [];
    private $createdDetailIds = [];
    private $createdRecordIds = [];
    
    public function __construct()
    {
        echo "AI SDR 测试数据准备工具\n";
        echo "客户ID: {$this->testClientId}\n";
        echo "用户ID: {$this->testUserId}\n";
        echo "========================\n\n";
    }
    
    /**
     * 准备所有测试数据
     */
    public function prepareAllTestData()
    {
        try {
            echo "开始准备AI SDR测试数据...\n\n";
            
            // 1. 创建运行中的AI SDR任务
            $this->createRunningTasks();
            
            // 2. 创建各阶段的任务详情
            $this->createTaskDetails();
            
            // 3. 创建任务执行记录
            $this->createTaskRecords();
            
            // 4. 创建客户档案数据
            $this->createClientProfile();
            
            // 5. 验证数据完整性
            $this->verifyTestData();
            
            echo "\n✅ AI SDR测试数据准备完成!\n";
            $this->printSummary();
            
        } catch (Exception $e) {
            echo "❌ 准备测试数据失败: " . $e->getMessage() . "\n";
            echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
        }
    }
    
    /**
     * 创建运行中的AI SDR任务
     */
    private function createRunningTasks()
    {
        echo "1. 创建运行中的AI SDR任务...\n";
        
        $taskConfigs = [
            [
                'source' => Constant::TASK_SOURCE_AI_SDR,
                'current_stage' => Constant::AI_SDR_STAGE_DIG,
                'end_stage' => Constant::AI_SDR_STAGE_EFFECTIVE,
                'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
                'email' => '<EMAIL>',
                'tags' => [1, 2]
            ],
            [
                'source' => Constant::TASK_SOURCE_AI_SDR,
                'current_stage' => Constant::AI_SDR_STAGE_REACHABLE,
                'end_stage' => Constant::AI_SDR_STAGE_MARKETING,
                'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
                'email' => '<EMAIL>',
                'tags' => [2, 3]
            ],
            [
                'source' => Constant::TASK_SOURCE_AI_SDR,
                'current_stage' => Constant::AI_SDR_STAGE_MARKETING,
                'end_stage' => Constant::AI_SDR_STAGE_HIGHVALUE,
                'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
                'email' => '<EMAIL>',
                'tags' => [1, 3]
            ]
        ];
        
        foreach ($taskConfigs as $index => $config) {
            $taskId = $this->createTask($config);
            if ($taskId) {
                $this->createdTaskIds[] = $taskId;
                echo "   ✓ 创建任务 #{$taskId} (阶段: {$config['current_stage']})\n";
            }
        }
        
        echo "   创建了 " . count($this->createdTaskIds) . " 个运行中的任务\n\n";
    }
    
    /**
     * 创建任务
     */
    private function createTask(array $config): ?int
    {
        $defaults = [
            'client_id' => $this->testClientId,
            'user_id' => $this->testUserId,
            'stat_total' => 0,
            'enable_flag' => 1,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $attributes = array_merge($defaults, $config);
        
        $task = new AiSdrTask($this->testClientId);
        foreach ($attributes as $key => $value) {
            if ($key !== 'client_id') {
                $task->$key = $value;
            }
        }
        
        if ($task->create()) {
            return $task->task_id;
        }
        
        return null;
    }
    
    /**
     * 创建各阶段的任务详情
     */
    private function createTaskDetails()
    {
        echo "2. 创建任务详情记录...\n";
        
        foreach ($this->createdTaskIds as $taskId) {
            $task = new AiSdrTask($this->testClientId, $taskId);
            
            // 为每个任务创建2-3个详情记录
            $detailCount = rand(2, 3);
            for ($i = 0; $i < $detailCount; $i++) {
                $detailId = $this->createTaskDetail($task, $i);
                if ($detailId) {
                    $this->createdDetailIds[] = $detailId;
                    echo "   ✓ 为任务 #{$taskId} 创建详情 #{$detailId}\n";
                }
            }
        }
        
        echo "   创建了 " . count($this->createdDetailIds) . " 个任务详情\n\n";
    }
    
    /**
     * 创建任务详情
     */
    private function createTaskDetail(AiSdrTask $task, int $index): ?int
    {
        $leadQualities = [
            Constant::LEAD_QUALITY_HIGH,
            Constant::LEAD_QUALITY_MEDIUM,
            Constant::LEAD_QUALITY_LOW
        ];
        
        $companyTypes = [
            ['Technology', 'Software'],
            ['Manufacturing', 'Industrial'],
            ['Trading', 'Distribution']
        ];
        
        $productIds = [
            [1, 2, 3],
            [2, 3, 4],
            [1, 4, 5]
        ];
        
        $homepages = [
            ['https://tech-company-' . $index . '.com'],
            ['https://manufacturing-corp-' . $index . '.com'],
            ['https://trading-firm-' . $index . '.com']
        ];
        
        $detail = new AiSdrTaskDetail($this->testClientId);
        $detail->task_id = $task->task_id;
        $detail->lead_id = rand(1000000, 9999999);
        $detail->user_id = $task->user_id;
        $detail->source = $task->source;
        $detail->stage = $task->current_stage;
        $detail->status = $this->getStatusForStage($task->current_stage);
        $detail->lead_quality = $leadQualities[$index % count($leadQualities)];
        $detail->usage_record_id = rand(1000, 9999);
        $detail->product_ids = $productIds[$index % count($productIds)];
        $detail->company_types = $companyTypes[$index % count($companyTypes)];
        $detail->public_homepage = $homepages[$index % count($homepages)];
        $detail->enable_flag = 1;
        
        // 设置阶段时间戳
        $this->setStageTimestamps($detail, $task->current_stage);
        
        $detail->create_time = date('Y-m-d H:i:s');
        $detail->update_time = date('Y-m-d H:i:s');
        
        if ($detail->create()) {
            return $detail->id;
        }
        
        return null;
    }
    
    /**
     * 根据阶段获取对应的状态
     */
    private function getStatusForStage(int $stage): int
    {
        switch ($stage) {
            case Constant::AI_SDR_STAGE_DIG:
                return Constant::DETAIL_STATUS_ADD;
            case Constant::AI_SDR_STAGE_REACHABLE:
                return Constant::DETAIL_STATUS_BACKGROUND_CHECKING;
            case Constant::AI_SDR_STAGE_MARKETING:
                return Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN;
            case Constant::AI_SDR_STAGE_EFFECTIVE:
                return Constant::DETAIL_STATUS_EFFECTIVE;
            case Constant::AI_SDR_STAGE_HIGHVALUE:
                return Constant::DETAIL_STATUS_HIGH_VALUE_LEADS;
            default:
                return Constant::DETAIL_STATUS_ADD;
        }
    }
    
    /**
     * 设置阶段时间戳
     */
    private function setStageTimestamps(AiSdrTaskDetail $detail, int $currentStage): void
    {
        $baseTime = strtotime('-1 day');
        
        // 设置已完成阶段的时间戳
        if ($currentStage >= Constant::AI_SDR_STAGE_DIG) {
            $detail->stage_dig_time = date('Y-m-d H:i:s', $baseTime);
        }
        if ($currentStage >= Constant::AI_SDR_STAGE_REACHABLE) {
            $detail->stage_reachable_time = date('Y-m-d H:i:s', $baseTime + 3600);
        }
        if ($currentStage >= Constant::AI_SDR_STAGE_MARKETING) {
            $detail->stage_marketing_time = date('Y-m-d H:i:s', $baseTime + 7200);
        }
        if ($currentStage >= Constant::AI_SDR_STAGE_EFFECTIVE) {
            $detail->stage_effective_time = date('Y-m-d H:i:s', $baseTime + 10800);
        }
        if ($currentStage >= Constant::AI_SDR_STAGE_HIGHVALUE) {
            $detail->stage_highvalue_time = date('Y-m-d H:i:s', $baseTime + 14400);
        }

        // 未完成阶段设置为默认值
        if ($currentStage < Constant::AI_SDR_STAGE_DIG) {
            $detail->stage_dig_time = '1970-01-01 00:00:01';
        }
        if ($currentStage < Constant::AI_SDR_STAGE_REACHABLE) {
            $detail->stage_reachable_time = '1970-01-01 00:00:01';
        }
        if ($currentStage < Constant::AI_SDR_STAGE_MARKETING) {
            $detail->stage_marketing_time = '1970-01-01 00:00:01';
        }
        if ($currentStage < Constant::AI_SDR_STAGE_EFFECTIVE) {
            $detail->stage_effective_time = '1970-01-01 00:00:01';
        }
        if ($currentStage < Constant::AI_SDR_STAGE_HIGHVALUE) {
            $detail->stage_highvalue_time = '1970-01-01 00:00:01';
        }
    }
    
    /**
     * 创建任务执行记录
     */
    private function createTaskRecords()
    {
        echo "3. 创建任务执行记录...\n";
        
        foreach ($this->createdDetailIds as $detailId) {
            $detail = new AiSdrTaskDetail($this->testClientId, $detailId);
            
            // 为每个详情创建1-2个记录
            $recordCount = rand(1, 2);
            for ($i = 0; $i < $recordCount; $i++) {
                $recordId = $this->createTaskRecord($detail, $i);
                if ($recordId) {
                    $this->createdRecordIds[] = $recordId;
                    echo "   ✓ 为详情 #{$detailId} 创建记录 #{$recordId}\n";
                }
            }
        }
        
        echo "   创建了 " . count($this->createdRecordIds) . " 个执行记录\n\n";
    }
    
    /**
     * 创建任务记录
     */
    private function createTaskRecord(AiSdrTaskDetail $detail, int $index): ?int
    {
        $recordTypes = [
            Constant::RECORD_TYPE_ADD_LEAD,
            Constant::RECORD_TYPE_BACKGROUND_CHECK,
            Constant::RECORD_TYPE_CREATE_MARKETING_PLAN,
            Constant::RECORD_TYPE_EXECUTE_MARKETING_PLAN
        ];
        
        $record = new AiSdrTaskRecord($detail->task_id);
        $record->task_id = $detail->task_id;
        $record->detail_id = $detail->id;
        $record->lead_id = $detail->lead_id;
        $record->type = $recordTypes[$index % count($recordTypes)];
        $record->data = [
            'action' => 'test_action_' . $index,
            'result' => 'success',
            'timestamp' => time()
        ];
        $record->estimate_time = date('Y-m-d H:i:s');
        $record->executed_time = date('Y-m-d H:i:s');
        $record->refer_type = 0;
        $record->refer_id = 0;
        $record->enable_flag = 1;
        $record->create_time = date('Y-m-d H:i:s');
        $record->update_time = date('Y-m-d H:i:s');
        
        if ($record->create()) {
            return $record->record_id;
        }
        
        return null;
    }
    
    /**
     * 创建客户档案数据
     */
    private function createClientProfile()
    {
        echo "4. 创建客户档案数据...\n";
        
        try {
            // 这里可以添加客户档案创建逻辑
            // 由于ClientProfile类可能不存在，我们跳过这一步
            echo "   ⚠️  客户档案创建跳过 (需要根据实际情况实现)\n\n";
        } catch (Exception $e) {
            echo "   ⚠️  客户档案创建失败: " . $e->getMessage() . "\n\n";
        }
    }
    
    /**
     * 验证测试数据
     */
    private function verifyTestData()
    {
        echo "5. 验证测试数据完整性...\n";
        
        // 验证任务
        foreach ($this->createdTaskIds as $taskId) {
            $task = new AiSdrTask($this->testClientId, $taskId);
            if ($task->isNew()) {
                throw new Exception("任务 #{$taskId} 验证失败");
            }
        }
        echo "   ✓ 所有任务验证通过\n";
        
        // 验证详情
        foreach ($this->createdDetailIds as $detailId) {
            $detail = new AiSdrTaskDetail($this->testClientId, $detailId);
            if ($detail->isNew()) {
                throw new Exception("详情 #{$detailId} 验证失败");
            }
        }
        echo "   ✓ 所有详情验证通过\n";
        
        // 验证记录
        foreach ($this->createdRecordIds as $recordId) {
            $record = new AiSdrTaskRecord($this->testClientId, $recordId);
            if ($record->isNew()) {
                throw new Exception("记录 #{$recordId} 验证失败");
            }
        }
        echo "   ✓ 所有记录验证通过\n\n";
    }
    
    /**
     * 打印摘要信息
     */
    private function printSummary()
    {
        echo "📊 测试数据摘要:\n";
        echo "================\n";
        echo "创建的任务数量: " . count($this->createdTaskIds) . "\n";
        echo "创建的详情数量: " . count($this->createdDetailIds) . "\n";
        echo "创建的记录数量: " . count($this->createdRecordIds) . "\n";
        echo "\n";
        
        if (!empty($this->createdTaskIds)) {
            echo "任务ID列表: " . implode(', ', $this->createdTaskIds) . "\n";
        }
        
        echo "\n💡 使用提示:\n";
        echo "- 这些测试数据可用于AI SDR功能测试\n";
        echo "- 任务处于不同阶段，可测试各种业务场景\n";
        echo "- 数据包含完整的关联关系和时间戳\n";
        echo "- 可以通过数据库事务回滚来清理测试数据\n";
    }
    
    /**
     * 清理测试数据
     */
    public function cleanupTestData()
    {
        echo "🧹 清理测试数据...\n";
        
        // 清理记录
        foreach ($this->createdRecordIds as $recordId) {
            try {
                $record = new AiSdrTaskRecord($this->testClientId, $recordId);
                if (!$record->isNew()) {
                    $record->delete();
                }
            } catch (Exception $e) {
                // 忽略删除错误
            }
        }
        
        // 清理详情
        foreach ($this->createdDetailIds as $detailId) {
            try {
                $detail = new AiSdrTaskDetail($this->testClientId, $detailId);
                if (!$detail->isNew()) {
                    $detail->delete();
                }
            } catch (Exception $e) {
                // 忽略删除错误
            }
        }
        
        // 清理任务
        foreach ($this->createdTaskIds as $taskId) {
            try {
                $task = new AiSdrTask($this->testClientId, $taskId);
                if (!$task->isNew()) {
                    $task->delete();
                }
            } catch (Exception $e) {
                // 忽略删除错误
            }
        }
        
        echo "✅ 测试数据清理完成\n";
    }
}

// 执行测试数据准备
if (php_sapi_name() === 'cli') {
    $preparer = new AiSdrTestDataPreparer();
    
    // 检查命令行参数
    if (isset($argv[1]) && $argv[1] === 'cleanup') {
        $preparer->cleanupTestData();
    } else {
        $preparer->prepareAllTestData();
    }
}
